﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using DevExpress.Data.Filtering;
using DevExpress.Data.Filtering.Helpers;
using DevExpress.Skins;
using DevExpress.Utils;
using DevExpress.Utils.Menu;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraEditors.Filtering;
using DevExpress.XtraEditors.ViewInfo;
using DevExpress.XtraTreeList;
using DevExpress.XtraTreeList.Menu;
using DevExpress.XtraTreeList.Nodes;
using DevExpress.XtraTreeList.ViewInfo;
using eBay.Service.Core.Soap;
using uBuyFirst.Filters;
using uBuyFirst.Grid;
using uBuyFirst.Network;
using uBuyFirst.Prefs;
using uBuyFirst.Stats;
using uBuyFirst.SubSearch;
using uBuyFirst.Tools;
using PopupMenuShowingEventArgs = DevExpress.XtraTreeList.PopupMenuShowingEventArgs;

namespace uBuyFirst
{
    public partial class Form1
    {
        private TreeListHitInfo _pressedHitInfo;

        private void CheckSelected_ItemClick(object sender, EventArgs e)
        {
            // Create a copy of the selection to avoid collection modification exception
            var selectedNodes = treeList1.Selection.ToArray();

            foreach (var node in selectedNodes)
            {
                node.Checked = true;
                var dataRecord = treeList1.GetDataRecordByNode(node);
                switch (dataRecord)
                {
                    case Keyword2Find kw:
                        kw.KeywordEnabled = CheckState.Checked;
                        break;
                    case ChildTerm childTerm:
                        childTerm.Enabled = true;
                        break;
                    case Search.KeywordFolder folder:
                        // Cascade check to all keywords and subfolders
                        CascadeFolderCheckState(folder, CheckState.Checked);
                        break;
                }
            }

            treeList1.RefreshDataSource();
        }

        private void UnCheckSelected_ItemClick(object sender, EventArgs e)
        {
            // Create a copy of the selection to avoid collection modification exception
            var selectedNodes = treeList1.Selection.ToArray();

            foreach (var node in selectedNodes)
            {
                node.Checked = false;
                var dataRecord = treeList1.GetDataRecordByNode(node);
                switch (dataRecord)
                {
                    case Keyword2Find kw:
                        kw.KeywordEnabled = CheckState.Unchecked;
                        break;
                    case ChildTerm childTerm:
                        childTerm.Enabled = false;
                        break;
                    case Search.KeywordFolder folder:
                        // Cascade uncheck to all keywords and subfolders
                        CascadeFolderCheckState(folder, CheckState.Unchecked);
                        break;
                }
            }

            treeList1.RefreshDataSource();
        }

        private void collapseAll_ItemClick(object sender, EventArgs e)
        {
            treeList1.CollapseAll();
        }

        private void expandAll_ItemClick(object sender, EventArgs e)
        {
            treeList1.ExpandAll();
        }

        private void delete_ItemClick(object sender, EventArgs e)
        {
            RemoveSearchItem();
        }

        private void NewEBaySearch_ItemClick(object sender, EventArgs e)
        {
            NewEbaySearch("New eBay Search");
            Analytics.AddEvent("", "KeywordAdded", 1);
        }

        private void NewSubSearch_ItemClick(object sender, EventArgs e)
        {
            NewChildTerm();
        }

        private void NewCopy_ItemClick(object sender, EventArgs e)
        {
            NewNodeCopy();
        }

        private void btnNewSearchQuery_Click(object sender, EventArgs e)
        {
            NewEbaySearch("New eBay Search");
            Analytics.AddEvent("", "KeywordAdded", 1);
        }

        private void repositoryItemButtonEditAlias_ButtonClick(object sender, ButtonPressedEventArgs e)
        {
            if (e.Button.Caption == "Add Sub Search")
                NewChildTerm();
        }

        private void treeList1_AfterCheckNode(object sender, NodeEventArgs e)
        {
            var dataRecord = treeList1.GetDataRecordByNode(e.Node);
            switch (dataRecord)
            {
                case Keyword2Find kw:
                    kw.KeywordEnabled = e.Node.CheckState;
                    break;
                case ChildTerm childTerm:
                    childTerm.Enabled = e.Node.Checked;
                    break;
                case Search.KeywordFolder folder:
                    // Cascade enable/disable to all keywords and subfolders
                    CascadeFolderCheckState(folder, e.Node.CheckState);
                    break;
            }
        }

        private void treeList1_AfterExpand(object sender, NodeEventArgs e)
        {
            for (var j = 0; j < e.Node.Nodes.Count; j++)
            {
                SetNodeChecked(e.Node.Nodes[j]);
            }
        }

        private void treeList1_CustomNodeCellEdit(object sender, GetCustomNodeCellEditEventArgs e)
        {
            if (e.Node == null)
                return;

            switch (e.Node.Level)
            {
                case 0:
                    switch (e.Column.FieldName)
                    {
                        case "Alias":

                            break;

                        case "Type":

                            break;

                        case "Keywords":
                        case "Price Min":
                        case "Price Max":
                        case "Zip":
                            e.RepositoryItem = repositoryItemEditTextData;

                            break;

                        case "Category ID":
                            break;

                        case "Site":
                            e.RepositoryItem = repositoryItemComboBoxSite;

                            break;

                        case "Located in":
                            e.RepositoryItem = repositoryItemComboBoxLocatedIn;

                            break;

                        case "Ships to":
                            e.RepositoryItem = repositoryItemComboBoxShipsTo;

                            break;

                        case "Filter":
                            e.RepositoryItem = repositoryItemEmpty4Filter;
                            break;
                    }

                    break;

                case 1:
                    switch (e.Column.FieldName)
                    {
                        case "Alias":
                        case "Keywords":
                        case "Price Min":
                        case "Price Max":
                            e.RepositoryItem = repositoryItemEditTextData;

                            break;
                        case "Category ID":
                            break;

                        case "Located in":
                        case "Site":
                        case "Ships to":
                        case "Zip":
                        case "Sellers":
                        case "Seller type":
                        case "Interval":
                        case "Threads":
                        case "View":
                        case "ListingType":
                            e.RepositoryItem = repositoryItemEmpty4Filter;

                            break;

                        case "Filter":
                            e.RepositoryItem = repositoryItemPopupContainerEditFilter;
                            break;
                    }

                    break;
            }
        }

        private void treeList1_InvalidNodeException(object sender, InvalidNodeExceptionEventArgs e)
        {
            e.ExceptionMode = ExceptionMode.NoAction;
        }

        private void treeList1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Insert && e.Modifiers == Keys.None)
            {
                e.Handled = true;
                NewEbaySearch("New eBay Search");
                Analytics.AddEvent("", "KeywordAdded", 1);
            }

            if (e.KeyCode == Keys.Insert && e.Modifiers == Keys.Control)
            {
                e.Handled = true;
                NewChildTerm();
            }

            if (e.KeyCode == Keys.Delete && e.Modifiers == Keys.None)
            {
                e.Handled = true;
                RemoveSearchItem();
            }

            if (e.KeyCode == Keys.F2 || e.KeyCode == Keys.Enter)
            {
                e.Handled = true;
                UserSettings.CanShowEbaySearchEditor = true;
                treeList1.ShowEditor();
                switch (treeList1.FocusedColumn.FieldName)
                {
                    case "Located in":
                    case "Ships to":
                    case "Site":
                        var editor = treeList1.ActiveEditor as ComboBoxEdit;
                        editor?.ShowPopup();

                        break;
                    case "Category ID":
                        var teditor = treeList1.ActiveEditor as TreeListLookUpEdit;
                        teditor?.ShowPopup();

                        break;

                    case "Condition":
                    case "ListingType":
                        var ceditor = treeList1.ActiveEditor as CheckedComboBoxEdit;
                        ceditor?.ShowPopup();

                        break;

                    case "View":
                        var veditor = treeList1.ActiveEditor as MRUEdit;
                        veditor?.ShowPopup();

                        break;
                    case "Filter":
                        var peditor = treeList1.ActiveEditor as PopupContainerEdit;
                        peditor?.ShowPopup();

                        break;
                }
            }
        }

        private void treeList1_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            UserSettings.CanShowEbaySearchEditor = true;
            treeList1.ShowEditor();
        }

        private void treeList1_MouseDown(object sender, MouseEventArgs e)
        {
            if (sender is TreeList tree)
            {
                var hitInfo = tree.CalcHitInfo(e.Location);
                if (e.Button == MouseButtons.Right && hitInfo.HitInfoType == HitInfoType.Cell && !treeList1.HasColumnErrors)
                {
                    if (tree.Selection.Count > 1)
                    {
                        hitInfo.Node.Selected = true;

                        return;
                    }

                    tree.Selection.Clear();
                    hitInfo.Node.Selected = true;
                }
            }

            _pressedHitInfo = treeList1.CalcHitInfo(e.Location);
            // Count top-level items (folders and keywords at root level)
            var topLevelCount = treeList1.Nodes.Count(n =>
                treeList1.GetDataRecordByNode(n) is Search.KeywordFolder ||
                (treeList1.GetDataRecordByNode(n) is Keyword2Find && n.ParentNode == null));

            if (e.Button == MouseButtons.Left && treeList1.Selection.Count == topLevelCount)
            {
                while (treeList1.Selection.Count > 0)
                    treeList1.Selection.First().Selected = false;
            }

            if (_pressedHitInfo.Node != null)
            {
                var ri = treeList1.ViewInfo.GetRowInfoByPoint(_pressedHitInfo.MousePoint);

                if (ri == null || ri.Cells == null)
                    return;

                CellInfo cellInfo = null;
                foreach (var cell in ri.Cells)
                {
                    if (cell.Bounds.Contains(_pressedHitInfo.MousePoint))
                        cellInfo = cell;
                }

                if (cellInfo != null)
                {
                    var vi = cellInfo.EditorViewInfo as ButtonEditViewInfo;
                    var buttonInfo = vi?.ButtonInfoByPoint(_pressedHitInfo.MousePoint);
                    if (buttonInfo != null)
                    {
                        if (buttonInfo.Bounds.Contains(_pressedHitInfo.MousePoint))
                        {
                            UserSettings.CanShowEbaySearchEditor = true;

                            //_showButtons = true;
                            return;
                        }
                    }

                    if (cellInfo.EditorViewInfo is CheckEditViewInfo
                        || cellInfo.Column.Name == "cEnabled"
                        )
                    {
                        UserSettings.CanShowEbaySearchEditor = true;

                        return;
                    }
                }

                if (cellInfo != null && cellInfo.Focused)
                {
                    // Cancel the editor if the cell is not focused
                    UserSettings.CanShowEbaySearchEditor = true;
                    return;
                }
            }

            UserSettings.CanShowEbaySearchEditor = false;
        }

        private void treeList1_NodeCellStyle(object sender, GetCustomNodeCellStyleEventArgs e)
        {
            if (!e.Node.Checked && !e.Node.Selected)
            {
                e.Appearance.BackColor = Color.Gainsboro;
            }

            var dataRecord = treeList1.GetDataRecordByNode(e.Node);

            // Apply special styling for folders
            if (dataRecord is Search.KeywordFolder)
            {
                e.Appearance.Font = new Font(e.Appearance.Font, FontStyle.Bold);
                e.Appearance.ForeColor = Color.DarkBlue;
            }
            // Apply styling for child terms (non-keyword, non-folder items)
            else if (dataRecord is ChildTerm
                && e.Column.FieldName != "Alias"
                && e.Column.FieldName != "Keywords"
                && e.Column.FieldName != "Price Min"
                && e.Column.FieldName != "Price Max"
                && e.Column.FieldName != "Search in description"
                && e.Column.FieldName != "Category ID"
                && e.Column.FieldName != "Condition"
                && e.Column.FieldName != "Filter")
            {
                var skin = CommonSkins.GetSkin(defaultLookAndFeel1.LookAndFeel);
                var skinElement = skin[CommonSkins.SkinScrollButton];
                if (skinElement != null)
                {
                    e.Appearance.BackColor = skinElement.Color.BackColor;
                }
            }

            if (e.Node.Level == 0 && e.Column.FieldName == "Filter")
            {
                e.Appearance.BackColor = Color.GhostWhite;
            }

            if (!e.Node.Checked)
            {
                if (e.Node.Selected)
                {
                    var skin = CommonSkins.GetSkin(defaultLookAndFeel1.LookAndFeel);
                    var skinElement = skin[CommonSkins.SkinGroupPanel];
                    if (skinElement != null)
                    {
                        var color = skinElement.Color.BackColor;
                        e.Appearance.BackColor = ControlPaint.Dark(color, -0.3f);
                    }
                }
                else
                {
                    var skin = CommonSkins.GetSkin(defaultLookAndFeel1.LookAndFeel);
                    var skinElement = skin[CommonSkins.SkinGroupPanel];
                    if (skinElement != null)
                    {
                        var color = skinElement.Color.BackColor;
                        e.Appearance.BackColor = ControlPaint.Dark(color, -0.1f);

                    }
                }
            }
        }

        private void treeList1_NodeChanged(object sender, NodeChangedEventArgs e)
        {
            if (e.ChangeType == NodeChangeTypeEnum.Add)
            {
                var dataRecord = treeList1.GetDataRecordByNode(e.Node);
                switch (dataRecord)
                {
                    case ChildTerm childTerm:
                        e.Node.Checked = childTerm.Enabled;
                        break;
                    case Keyword2Find keyword:
                        e.Node.CheckState = keyword.KeywordEnabled;
                        break;
                    case Search.KeywordFolder folder:
                        e.Node.Expanded = folder.IsExpanded;
                        break;
                }
            }
        }

        private void treeList1_Paint(object sender, PaintEventArgs e)
        {
            foreach (var node in treeList1.Selection)
            {
                var rowInfo = treeList1.ViewInfo?.RowsInfo[node];

                if (rowInfo != null)
                {
                    var pen = new Pen(Color.Black, 2);
                    var pen2 = new Pen(Color.DeepSkyBlue, 3);
                    if (treeList1.FocusedColumn != null && rowInfo.Cells.Count > treeList1.FocusedColumn.VisibleIndex && treeList1.FocusedColumn.VisibleIndex > -1)
                    {
                        //e.Graphics.DrawRectangle(pen2, ((CellInfo)rowInfo.Cells[treeList1.FocusedColumn.ColumnIndex]).Bounds);
                        e.Graphics.DrawRectangle(pen2, rowInfo.Cells[treeList1.FocusedColumn.VisibleIndex].Bounds);
                    }

                    e.Graphics.DrawRectangle(pen, rowInfo.TotalBounds);
                }
            }
        }

        private void treeList1_PopupMenuShowing(object sender, PopupMenuShowingEventArgs e)
        {
            try
            {
                if (e.Menu is TreeListNodeMenu nodeMenu && !treeList1.HasColumnErrors)
                {
                    if (nodeMenu.Node == null)
                        return;

                    treeList1.SetFocusedNode(nodeMenu.Node);

                    var dataRecord = treeList1.GetDataRecordByNode(nodeMenu.Node);

                    // Add context-specific menu items based on node type
                    switch (dataRecord)
                    {
                        case Search.KeywordFolder folder:
                            AddFolderMenuItems(nodeMenu, folder);
                            break;
                        case Keyword2Find keyword:
                            AddKeywordMenuItems(nodeMenu, keyword);
                            break;
                        case ChildTerm childTerm:
                            AddChildTermMenuItems(nodeMenu, childTerm);
                            break;
                        default:
                            // Fallback to original logic for unknown types
                            var dXMenuItemNewEBaySearch = new DXMenuItem("Add eBay Search (Insert)", NewEBaySearch_ItemClick);
                            nodeMenu.Items.Add(dXMenuItemNewEBaySearch);
                            break;
                    }

                    var menuCheckSelected = new DXMenuItem("Check Selected", CheckSelected_ItemClick);
                    menuCheckSelected.BeginGroup = true;
                    nodeMenu.Items.Add(menuCheckSelected);
                    var menuUnCheckSelected = new DXMenuItem("Uncheck Selected", UnCheckSelected_ItemClick);
                    nodeMenu.Items.Add(menuUnCheckSelected);

                    var menuExpandAll = new DXMenuItem("Expand all", expandAll_ItemClick);
                    menuExpandAll.BeginGroup = true;
                    nodeMenu.Items.Add(menuExpandAll);

                    var dXMenuItemCollapseAll = new DXMenuItem("Collapse all", collapseAll_ItemClick);
                    nodeMenu.Items.Add(dXMenuItemCollapseAll);

                    // Add special menu items for OutOfStock keywords
                    if (treeList1.GetDataRecordByNode(treeList1.FocusedNode) is Keyword2Find kw)
                    {
                        if (kw.ListingType.Contains(ListingType.OutOfStock))
                        {
                            menuCheckSelected.BeginGroup = true;
                            var dXMenuItemAddFromWatchList = new DXMenuItem("Import and replace ItemIDs from Ebay Watchlist",
                                AddFromWatchList_ItemClick);
                            nodeMenu.Items.Add(dXMenuItemAddFromWatchList);
                        }
                    }

                    var menuDelete = new DXMenuItem("Delete [" + treeList1.Selection.Count + "] " + "item(s)", delete_ItemClick);
                    menuDelete.BeginGroup = true;
                    nodeMenu.Items.Add(menuDelete);
                }

                if (e.Menu is TreeListColumnMenu)
                {
                    var height = ((TreeList)sender).RowHeight;
                    var firstNode = treeList1.ViewInfo.RowsInfo.FirstOrDefault();

                    //// --- Add Fetch Search Results Item ---
                    //var menuItemFetchSearchResults = new DXMenuItem("Fetch Search Results", OnFetchSearchResultsTreelistClick);
                    //// Optional: Assign an SvgImage if available
                    //// menuItemFetchSearchResults.SvgImage = Resources.SearchIcon;
                    //menuItemFetchSearchResults.Tag = nodeMenu.Node; // Pass the node context
                    //menuItemFetchSearchResults.BeginGroup = true;
                    //nodeMenu.Items.Add(menuItemFetchSearchResults);
                    //// --- End Fetch Search Results Item ---

                    if (firstNode != null)
                        height = firstNode.Bounds.Height;

                    var nodeDecreaseHeight = new DXMenuItem("-Row height", (o, args) => ((TreeList)sender).RowHeight = height - 5);
                    e.Menu.Items.Add(nodeDecreaseHeight);
                    var nodeIncreaseHeight = new DXMenuItem("+Row height", (o, args) => ((TreeList)sender).RowHeight = height + 5);
                    e.Menu.Items.Add(nodeIncreaseHeight);
                }
            }
            catch (Exception ex)
            {
                if (ProgramState.Isdebug)
                {
                    XtraMessageBox.Show("treeList1_PopupMenuShowing: " + ex.Message);
                }

                Log.Error("{0}, {1} Exception:{2}", ProgramState.UBFVersion, ProgramState.HWID + ProgramState.SerialNumber, ex);
            }
        }

        private void OnFetchSearchResultsTreelistClick(object sender, EventArgs e)
        {
            // Placeholder for future implementation for the treelist
            // Currently does nothing as requested.
            var menuItem = (DXMenuItem)sender;
            if (menuItem.Tag is TreeListNode node)
            {
                object dataRecord = treeList1.GetDataRecordByNode(node);
                if (dataRecord is Keyword2Find kw)
                {
                    // Example: Log or show a message for Keyword2Find
                    // Log?.Info($"Fetch Search Results clicked for Keyword: {kw.Alias}");
                    // XtraMessageBox.Show($"Fetch Search Results for '{kw.Alias}' is not yet implemented.", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else if (dataRecord is ChildTerm ct)
                {
                    // Example: Log or show a message for ChildTerm
                    // Log?.Info($"Fetch Search Results clicked for Child Term: {ct.Alias}");
                    // XtraMessageBox.Show($"Fetch Search Results for '{ct.Alias}' is not yet implemented.", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }


        private void AddFromWatchList_ItemClick(object sender, EventArgs e)
        {
            if (treeList1.FocusedNode is { Level: 0 })
                if (treeList1.GetDataRecordByNode(treeList1.FocusedNode) is Keyword2Find kw)
                {
                    if (kw.ListingType.Contains(ListingType.OutOfStock))
                    {
                        var ebayAccount = Form1.EBayAccountsList.FirstOrDefault();
                        if (ebayAccount == null)
                        {
                            XtraMessageBox.Show("No ebay accounts found. Please add your eBay account to uBuyFirst.");
                            return;
                        }

                        var apiContext =
                            ConnectionConfig.GetApiContextPlaceOffer(kw.EBaySite.SiteCode, ebayAccount.TokenPo);
                        var itemIds = ApiService.GetAllWatchlistItemIds(apiContext);

                        XtraMessageBox.Show("Imported " + itemIds.Count + " new itemIDs from Ebay Watchlist.\r\nPlease, click Stop/Start to restart your search.");
                        kw.Kws = string.Join(",", itemIds);
                        treeList1.RefreshDataSource();
                    }
                }
        }

        private void treeList1_ShowingEditor(object sender, CancelEventArgs e)
        {
            if (!UserSettings.CanShowEbaySearchEditor)
            {
                e.Cancel = true;

                return;
            }

            if (repositoryItemButtonNewKeyword.TextEditStyle == TextEditStyles.HideTextEditor)
                e.Cancel = true;

            var currentEditor = (sender as TreeList)?.ActiveEditor;

            if (currentEditor != null)
                currentEditor.IsModified = true;

            if (ProgramState.Isdebug)
            {
                /*
                                if (treeList1.Selection.Count > 0)
                                {
                                    var node = treeList1.Selection[0];
                                    if (node.Level == 0)
                                    {
                                        if (treeList1.GetDataRecordByNode(node) is Keyword2Find kw) Text = kw.Alias;
                                    }
                                    else
                                    {
                                        var childTerm = treeList1.GetDataRecordByNode(node) as ChildTerm;
                                        var keyword2Find = childTerm.GetParent();
                                        var isnull = "";
                                        if (keyword2Find == null)
                                            isnull = "NULL";
                                        Text = isnull + "\t" + childTerm.Alias;
                                    }
                                    if (node.ParentNode != null)
                                        Text = node.Id + "|" + node.ParentNode.Nodes.IndexOf(node);
                                }
                                */
            }
        }

        private void treeList1_ShownEditor(object sender, EventArgs e)
        {
            var treeList = sender as TreeList;
            var currentEditor = treeList?.ActiveEditor;
            if (currentEditor != null)
            {
                currentEditor.IsModified = true;
            }

            UserSettings.CanShowEbaySearchEditor = false;
        }

        private void treeList1_ValidateNode(object sender, ValidateNodeEventArgs e)
        {
            var node = e.Node;

            if (node == null)
                return;

            var dataRecord = treeList1.GetDataRecordByNode(node);
            switch (dataRecord)
            {
                case Keyword2Find kw:
                    var uniqueAliasesDict = Helpers.CountStrings(_ebaySearches.ChildrenCore.Select(k => k.Alias).ToList());
                    e.Valid = ValidateKeyword2Find(kw, out _, uniqueAliasesDict);
                    treeList1.RefreshCell(e.Node, e.Node.TreeList.Columns["ListingType"]);
                    treeList1.RefreshCell(e.Node, e.Node.TreeList.Columns["Alias"]);
                    break;
                case ChildTerm kw:
                    {
                    if (string.IsNullOrEmpty(kw.Alias))
                    {
                        e.Valid = false;
                        treeList1.SetColumnError(cAlias, $"Alias field should not be empty. [{kw.Keywords}]");
                    }

                    if (string.IsNullOrEmpty(kw.Keywords))
                    {
                        e.Valid = false;
                        treeList1.SetColumnError(cKeywords, $"Keywords field should not be empty. [{kw.Alias}]");
                    }

                    var priceMin = kw.PriceMin;
                    var priceMax = kw.PriceMax;
                    if (priceMin < 0.01 || priceMin > 10000000)
                    {
                        e.Valid = false;
                        treeList1.SetColumnError(cPriceMin, $"Valid price range is 0.01 - 10,000,000. [{kw.Alias}]");
                    }

                    if (priceMax < 0.01 || priceMax > 10000000)
                    {
                        e.Valid = false;
                        treeList1.SetColumnError(cPriceMax, $"Valid price range is 0.01 - 10,000,000. [{kw.Alias}]");
                    }

                    if (kw.CategoryIDs.Length > 20)
                    {
                        e.Valid = false;
                        treeList1.SetColumnError(cCategoryID, $"Maximum number of categories for Sub Search is 20. [{kw.Alias}]");
                    }

                    if (!Regex.IsMatch(string.Join(",", kw.CategoryIDs), "^[0-9,\\s]*$", RegexOptions.None))
                    {
                        e.Valid = false;
                        treeList1.SetColumnError(cCategoryID, $"Category field should be empty OR contain only comma separated numbers. [{kw.Alias}]");
                    }
                    }
                    break;
                case Search.KeywordFolder folder:
                    // Validate folder name
                    if (string.IsNullOrEmpty(folder.Name))
                    {
                        e.Valid = false;
                        treeList1.SetColumnError(cAlias, "Folder name should not be empty.");
                    }
                    // Validate hierarchy
                    if (!folder.ValidateHierarchy())
                    {
                        e.Valid = false;
                        treeList1.SetColumnError(cAlias, "Circular reference detected in folder hierarchy.");
                    }
                    break;
            }

            if (e.Valid)
            {
                treeList1.ClearColumnErrors();
            }
        }

        private bool ValidateKeyword2Find(Keyword2Find kw, out string errorMessages, Dictionary<string, int> existingAliases)
        {
            errorMessages = "";
            var eValid = true;
            if (string.IsNullOrEmpty(kw.Alias))
            {
                eValid = false;
                var errorMessage = $"Alias field should not be empty. [{kw.Kws}]";
                errorMessages += errorMessage + "\r\n";
                treeList1.SetColumnError(cAlias, errorMessage);
            }
            else
            {
                kw.Alias = Helpers.MakeUniqAliasOnEdit(kw.Alias, existingAliases);
            }

            if (string.IsNullOrEmpty(kw.Kws))
            {
                if (LicenseUtility.CurrentLimits.SearchTermsCount != 10000)
                {
                    eValid = false;
                    var errorMessage = $"Keywords field should not be empty. \nWith Enterprise license you are able to leave Keywords field empty and search just by Category. [{kw.Alias}]";
                    errorMessages += errorMessage + "\r\n";
                    treeList1.SetColumnError(cKeywords, errorMessage);
                }

                if (string.IsNullOrEmpty(kw.Categories4Api))
                {
                    eValid = false;
                    var errorMessage = $"Please, fill either Keywords or Category field. [{kw.Alias}]";
                    errorMessages += errorMessage + "\r\n";
                    treeList1.SetColumnError(cKeywords, errorMessage);
                }

                if (kw.ListingType.Contains(ListingType.OutOfStock))
                {
                    eValid = false;
                    var errorMessage = $"Please, enter items IDs separated by commas [{kw.Alias}]";
                    errorMessages += errorMessage + "\r\n";
                    treeList1.SetColumnError(cKeywords, errorMessage);
                }
            }
            else
            {
                if (kw.ListingType.Contains(ListingType.OutOfStock))
                {
                    if (kw.Kws.Length > 10000 || kw.Kws.Length < 12)
                    {
                        eValid = false;
                        var errorMessage = $"Min/Max length for Out Of Stock Search is  12/3000. You have {kw.Kws.Length} [{kw.Alias}]";
                        errorMessages += errorMessage + "\r\n";
                        treeList1.SetColumnError(cKeywords, errorMessage);
                    }

                    var itemIDsPattern = "^((\\d{12})(,\\s*)*)*$";
                    if (!Regex.IsMatch(kw.Kws, itemIDsPattern))
                    {
                        eValid = false;
                        var errorMessage = $"Please, enter items IDs separated by commas [{kw.Alias}]";
                        errorMessages += errorMessage + "\r\n";
                        treeList1.SetColumnError(cKeywords, errorMessage);
                    }
                }
                else
                {
                    if (kw.Kws.Length > 350 || kw.Kws.Length < 2)
                    {
                        eValid = false;
                        var errorMessage = $"Min/Max length for eBay Search is  2/350. The maximum length for a single word is 98. [{kw.Alias}]";
                        errorMessages += errorMessage + "\r\n";
                        treeList1.SetColumnError(cKeywords, errorMessage);
                    }

                    if (kw.Kws.StartsWith("-"))
                    {
                        eValid = false;
                        var errorMessage = $"eBay Search cannot start with '-' symbol. [{kw.Alias}]";
                        errorMessages += errorMessage + "\r\n";
                        treeList1.SetColumnError(cKeywords, errorMessage);
                    }

                    var error = KeywordHelpers.ValidateLuceneKeyword(kw.Kws, kw.Categories4Api.Length > 0);
                    if (!string.IsNullOrEmpty(error))
                    {
                        eValid = false;
                        var errorMessage = $"[{kw.Alias}]\r\nKeywords field has incorrect value.\r\n"
                                           + "Common issues: \r\nNot matched parentheses.\r\nExtra commas.\r\n"
                                           + "Unsupported special chars like - \"(inches).\r\nCan't start with '*'.";
                        errorMessages += errorMessage + "\r\n";
                        treeList1.SetColumnError(cKeywords, errorMessage);
                    }
                }
            }

            if (kw.Categories4Api.Split(',').Length > 3)
            {
                eValid = false;
                var errorMessage = $"Maximum number of categories for eBay Search is 3. Best is to use 1 category. [{kw.Alias}]";
                errorMessages += errorMessage + "\r\n";
                treeList1.SetColumnError(cCategoryID, errorMessage);
            }

            //if (ConnectionConfig.BrowseAPIEnabled && !ConnectionConfig.TradingAPIEnabled && kw.Categories4Api.Split(',').Length > 1)
            //{
            //    eValid = false;
            //    var errorMessage = $"Maximum number of categories for eBay Search is 1. [{kw.Alias}]";
            //    errorMessages += errorMessage + "\r\n";
            //    treeList1.SetColumnError(cCategoryID, errorMessage);
            //}

            if (!Enum.IsDefined(typeof(CountryCodeType), kw.LocatedIn) && kw.LocatedIn != "Any")
            {
                eValid = false;
                var errorMessage = $"'Located in' value  [{kw.LocatedIn}] in search '{kw.Alias}' is invalid. Please, select a correct value.";
                errorMessages += errorMessage + "\r\n";
                treeList1.SetColumnError(cCategoryID, errorMessage);
            }

            if (!Enum.IsDefined(typeof(CountryCodeType), kw.AvailableTo))
            {
                eValid = false;
                var errorMessage = $"'Available to' value [{kw.AvailableTo}] in search '{kw.Alias}' is invalid. Please, select a correct value.";
                errorMessages += errorMessage + "\r\n";
                treeList1.SetColumnError(cCategoryID, errorMessage);
            }

            if (!Regex.IsMatch(kw.Categories4Api, "^[0-9,\\s]*$", RegexOptions.None))
            {
                eValid = false;
                var errorMessage = $"Category field should be empty OR contain only comma separated numbers. [{kw.Alias}]";
                errorMessages += errorMessage + "\r\n";
                treeList1.SetColumnError(cCategoryID, errorMessage);
            }

            var priceMin = kw.PriceMin;
            var priceMax = kw.PriceMax;
            if (priceMin < 0.01 || priceMin > 10000000)
            {
                eValid = false;
                var errorMessage = $"Valid price range is 0.01 - 10,000,000. [{kw.Alias}]";
                errorMessages += errorMessage + "\r\n";
                treeList1.SetColumnError(cPriceMin, errorMessage);
            }

            if (priceMax < 0.01 || priceMax > 10000000)
            {
                eValid = false;
                var errorMessage = $"Valid price range is 0.01 - 10,000,000. [{kw.Alias}]";
                errorMessages += errorMessage + "\r\n";
                treeList1.SetColumnError(cPriceMax, errorMessage);
            }

            if (kw.Sellers.Length > 10)
            {
                eValid = false;
                var errorMessage = $"Maximum number of sellers is 10. [{kw.Alias}]";
                errorMessages += errorMessage + "\r\n";
                treeList1.SetColumnError(cSellers, errorMessage);
            }

            return eValid;
        }

        #region KeywordDragDrop

        private void treeList1_DragDrop(object sender, DragEventArgs e)
        {
            if (sender is TreeList tl)
            {
                tl.ClearSorting();
                var p = tl.PointToClient(new Point(e.X, e.Y));
                var dragNode = e.Data.GetData(typeof(TreeListNode)) as TreeListNode;
                var targetNode = tl.CalcHitInfo(p).Node;

                if (dragNode == targetNode)
                    return;

                if (targetNode != null && dragNode != null)
                {
                    var dragData = treeList1.GetDataRecordByNode(dragNode);
                    var targetData = treeList1.GetDataRecordByNode(targetNode);

                    // Handle different drag & drop scenarios
                    switch (dragData)
                    {
                        case Search.KeywordFolder dragFolder:
                            HandleFolderDrop(dragFolder, targetData, targetNode);
                            break;
                        case Keyword2Find dragKeyword:
                            HandleKeywordDrop(dragKeyword, targetData, targetNode);
                            break;
                        case ChildTerm dragChildTerm:
                            HandleChildTermDrop(dragChildTerm, targetData, targetNode);
                            break;
                    }

                    treeList1.RefreshDataSource();
                    RefreshAllNodeStates();
                }
            }
        }

        private void treeList1_DragOver(object sender, DragEventArgs e)
        {
            var args = treeList1.GetDXDragEventArgs(e);
            if (args.Node == null)
            {
                if (args.HitInfo.HitInfoType == HitInfoType.Empty || args.TargetNode != null)
                    args.Effect = DragDropEffects.Copy;
                else
                    args.Effect = DragDropEffects.None;
                return;
            }

            if (args.Node != null && args.TargetNode != null)
            {
                var dragData = treeList1.GetDataRecordByNode(args.Node);
                var targetData = treeList1.GetDataRecordByNode(args.TargetNode);

                // Determine if the drop operation is valid
                bool canDrop = CanDropItem(dragData, targetData, args.DragInsertPosition);

                if (canDrop)
                {
                    args.Effect = DragDropEffects.Move;
                }
                else
                {
                    args.Effect = DragDropEffects.None;
                }
            }
            else
            {
                args.Effect = DragDropEffects.None;
            }
        }

        #endregion KeywordDragDrop

        #region TreeList Filter Popup

        private void btnSavePopupFilter_Click(object sender, EventArgs e)
        {
            if (filterControlTerm.Tag is ChildTerm term)
            {
                term.SubSearch = new XFilterClassChild();
                term.SubSearch.Action = "Keep rows";
                term.SubSearch.Alias = term.Alias;
                term.SubSearch.Enabled = true;
                term.SubSearch.FilterCriteria = filterControlTerm.FilterCriteria;
                if (!filterControlTerm.IsFilterCriteriaValid)
                {
                    popupContainerControl1.OwnerEdit?.ClosePopup();

                    return;
                }

                term.SubSearch.Rebuild();
                filterControlTerm.Tag = term;
            }

            //filterControlTerm.FilterCriteria = null;
            filterControlTerm.Tag = null;
            popupContainerControl1.OwnerEdit?.ClosePopup();
        }

        private void treeList1_BeforeLoadLayout(object sender, LayoutAllowEventArgs e)
        {
            if (treeList1.OptionsLayout.LayoutVersion != e.PreviousVersion)
            {
                e.Allow = false;
            }
        }

        private void filterControlTerm_FilterChanged(object sender, FilterChangedEventArgs e)
        {
            if (e.Action == FilterChangedAction.FieldNameChange)
            {
                if (((ClauseNode)e.CurrentNode).FirstOperand.PropertyName == "Condition")
                {
                    if (e.CurrentNode is ClauseNode node)
                    {
                        node.Operation = ClauseType.AnyOf;
                    }
                }
            }

            repositoryItemPopupContainerEditFilter.Tag = filterControlTerm.FilterString;

        }

        private void repositoryItemPopupContainerEditFilter_QueryPopUp(object sender, CancelEventArgs e)
        {
            //filterControlTerm.MaxOperandsCount = 0;
            var term = treeList1.GetDataRecordByNode(treeList1.FocusedNode) as ChildTerm;
            if (treeList1.GetDataRecordByNode(treeList1.FocusedNode) is Keyword2Find)
            {
                e.Cancel = true;

                return;
            }

            if (term != null)
            {
                FilterAdapter1.DataSource = GridBuilder.DefaultDataTable; //Obsolete?
                //lblTermName.Text = term.Alias;
                //filterControlTerm.FilterCriteria = term.SubSearch?.FilterCriteria;
                filterControlTerm.FilterCriteria = ((PopupContainerEdit)sender).EditValue as CriteriaOperator;
                filterControlTerm.Tag = term;
                using var colBlob = filterControlTerm.FilterColumns.GetFilterColumnByCaption("Blob");
                if (colBlob != null)
                    filterControlTerm.FilterColumns.Remove(colBlob);

                using var colSource = filterControlTerm.FilterColumns.GetFilterColumnByCaption("Source");
                if (colSource != null)
                    filterControlTerm.FilterColumns.Remove(colSource);
            }
            else
            {
                filterControlTerm.FilterCriteria = null;
                filterControlTerm.Tag = null;
            }
        }

        #endregion TreeList Filter Popup

        private void RepositoryItemCheckedComboBoxEditListingType_EditValueChanged(object sender, EventArgs e)
        {
            var s = ((CheckedComboBoxEdit)sender).EditValue.ToString();
            if (s.Contains("OutOfStock") && s != "OutOfStock")
            {
                ((CheckedComboBoxEdit)sender).EditValue = "OutOfStock";
                XtraMessageBox.Show(
                    "Out of stock monitoring is enabled.\r\nPlease, enter your items IDs separated by comma into \"Keywords\" column.\r\nBuyItNow and Auction search have been disabled.");
            }

            var parent = ((CheckedComboBoxEdit)sender).Parent;
            ((TreeList)parent).PostEditor();
            ((TreeList)parent).Update();
            //((TreeList) parent).RefreshDataSource(); //
        }

        private void repositoryItemCheckEditEnabled_EditValueChanged(object sender, EventArgs e)
        {
            // Handle direct checkbox clicks in the Enabled column
            if (sender is CheckEdit checkEdit && checkEdit.Parent is TreeList treeList)
            {
                var focusedNode = treeList.FocusedNode;
                if (focusedNode != null)
                {
                    var dataRecord = treeList.GetDataRecordByNode(focusedNode);
                    var isChecked = checkEdit.Checked;
                    var checkState = isChecked ? CheckState.Checked : CheckState.Unchecked;

                    switch (dataRecord)
                    {
                        case Keyword2Find kw:
                            kw.KeywordEnabled = checkState;
                            break;
                        case ChildTerm childTerm:
                            childTerm.Enabled = isChecked;
                            break;
                        case Search.KeywordFolder folder:
                            // Cascade enable/disable to all keywords and subfolders
                            CascadeFolderCheckState(folder, checkState);
                            break;
                    }

                    // Update the node's check state to match
                    focusedNode.CheckState = checkState;
                }
            }
        }

        #region Context Menu Helpers

        private void AddFolderMenuItems(TreeListNodeMenu nodeMenu, Search.KeywordFolder folder)
        {
            var menuNewFolder = new DXMenuItem("New Folder", NewFolder_ItemClick);
            nodeMenu.Items.Add(menuNewFolder);

            var menuNewKeyword = new DXMenuItem("Add eBay Search", NewEBaySearch_ItemClick);
            nodeMenu.Items.Add(menuNewKeyword);

            var menuRenameFolder = new DXMenuItem("Rename Folder", RenameFolder_ItemClick);
            menuRenameFolder.BeginGroup = true;
            nodeMenu.Items.Add(menuRenameFolder);

            var menuDeleteFolder = new DXMenuItem("Delete Folder", DeleteFolder_ItemClick);
            nodeMenu.Items.Add(menuDeleteFolder);
        }

        private void AddKeywordMenuItems(TreeListNodeMenu nodeMenu, Keyword2Find keyword)
        {
            var dXMenuItemNewEBaySearch = new DXMenuItem("Add eBay Search (Insert)", NewEBaySearch_ItemClick);
            nodeMenu.Items.Add(dXMenuItemNewEBaySearch);

            var menuNewSubSearch = new DXMenuItem("New Sub Search (Insert)", NewSubSearch_ItemClick);
            nodeMenu.Items.Add(menuNewSubSearch);

            var dXMenuItem = new DXMenuItem("Duplicate eBay Search", NewCopy_ItemClick);
            dXMenuItem.BeginGroup = true;
            nodeMenu.Items.Add(dXMenuItem);
        }

        private void AddChildTermMenuItems(TreeListNodeMenu nodeMenu, ChildTerm childTerm)
        {
            var menuNewSubSearch = new DXMenuItem("New Sub Search (Insert)", NewSubSearch_ItemClick);
            nodeMenu.Items.Add(menuNewSubSearch);

            var dXMenuItem = new DXMenuItem("Duplicate Sub Search", NewCopy_ItemClick);
            dXMenuItem.BeginGroup = true;
            nodeMenu.Items.Add(dXMenuItem);
        }

        #endregion

        #region Drag & Drop Helpers

        private bool CanDropItem(object dragData, object targetData, DragInsertPosition position)
        {
            switch (dragData)
            {
                case Search.KeywordFolder dragFolder:
                    switch (targetData)
                    {
                        case Search.KeywordFolder targetFolder:
                            // Can drop folder into another folder (as child) or reorder folders
                            return !dragFolder.IsAncestorOf(targetFolder) && dragFolder != targetFolder;
                        case Keyword2Find:
                            // Can't drop folder on keyword
                            return false;
                        case ChildTerm:
                            // Can't drop folder on child term
                            return false;
                    }
                    break;
                case Keyword2Find:
                    switch (targetData)
                    {
                        case Search.KeywordFolder:
                            // Can drop keyword into folder
                            return position == DragInsertPosition.AsChild;
                        case Keyword2Find:
                            // Can reorder keywords
                            return position == DragInsertPosition.Before || position == DragInsertPosition.After;
                        case ChildTerm:
                            // Can't drop keyword on child term
                            return false;
                    }
                    break;
                case ChildTerm:
                    switch (targetData)
                    {
                        case Search.KeywordFolder:
                            // Can't drop child term on folder
                            return false;
                        case Keyword2Find:
                            // Can drop child term into keyword
                            return position == DragInsertPosition.AsChild;
                        case ChildTerm:
                            // Can reorder child terms
                            return position == DragInsertPosition.Before || position == DragInsertPosition.After;
                    }
                    break;
            }
            return false;
        }

        private void HandleFolderDrop(Search.KeywordFolder dragFolder, object targetData, TreeListNode targetNode)
        {
            switch (targetData)
            {
                case Search.KeywordFolder targetFolder:
                    // Move folder into another folder or reorder
                    if (dragFolder.ParentFolder != null)
                    {
                        dragFolder.ParentFolder.Children.Remove(dragFolder);
                    }
                    else
                    {
                        _ebaySearches.Folders.Remove(dragFolder);
                    }

                    dragFolder.ParentFolder = targetFolder;
                    targetFolder.Children.Add(dragFolder);
                    break;
            }
        }

        private void HandleKeywordDrop(Keyword2Find dragKeyword, object targetData, TreeListNode targetNode)
        {
            switch (targetData)
            {
                case Search.KeywordFolder targetFolder:
                    // Move keyword into folder
                    if (dragKeyword.ParentFolder != null)
                    {
                        dragKeyword.ParentFolder.Keywords.Remove(dragKeyword);
                    }

                    dragKeyword.ParentFolder = targetFolder;
                    targetFolder.Keywords.Add(dragKeyword);
                    break;
                case Keyword2Find targetKeyword:
                    // Reorder keywords within same parent
                    if (dragKeyword.ParentFolder == targetKeyword.ParentFolder)
                    {
                        var parentFolder = dragKeyword.ParentFolder;
                        if (parentFolder != null)
                        {
                            var dragIndex = parentFolder.Keywords.IndexOf(dragKeyword);
                            var targetIndex = parentFolder.Keywords.IndexOf(targetKeyword);

                            parentFolder.Keywords.RemoveAt(dragIndex);
                            parentFolder.Keywords.Insert(targetIndex, dragKeyword);
                        }
                    }
                    break;
            }
        }

        private void HandleChildTermDrop(ChildTerm dragChildTerm, object targetData, TreeListNode targetNode)
        {
            switch (targetData)
            {
                case Keyword2Find targetKeyword:
                    // Move child term to different keyword
                    var fromKeyword = dragChildTerm.GetParent();
                    if (fromKeyword != null)
                    {
                        dragChildTerm.RemoveFromParent(fromKeyword);
                        dragChildTerm.ChangeParent(targetKeyword);
                    }
                    break;
                case ChildTerm targetChildTerm:
                    // Reorder child terms within same keyword
                    var parentKeyword = dragChildTerm.GetParent();
                    var targetParentKeyword = targetChildTerm.GetParent();

                    if (parentKeyword == targetParentKeyword && parentKeyword != null)
                    {
                        var dragIndex = parentKeyword.ChildrenCore.IndexOf(dragChildTerm);
                        var targetIndex = parentKeyword.ChildrenCore.IndexOf(targetChildTerm);

                        parentKeyword.ChildrenCore.RemoveAt(dragIndex);
                        parentKeyword.ChildrenCore.Insert(targetIndex, dragChildTerm);
                    }
                    break;
            }
        }

        private void RefreshAllNodeStates()
        {
            for (var i = 0; i < treeList1.Nodes.Count; i++)
            {
                SetNodeChecked(treeList1.Nodes[i]);
                RefreshNodeChildren(treeList1.Nodes[i]);
            }
        }

        private void RefreshNodeChildren(TreeListNode node)
        {
            for (var j = 0; j < node.Nodes.Count; j++)
            {
                SetNodeChecked(node.Nodes[j]);
                RefreshNodeChildren(node.Nodes[j]);
            }
        }

        #endregion

        #region Folder Check State Management

        /// <summary>
        /// Cascades folder check state to all keywords and subfolders
        /// </summary>
        private void CascadeFolderCheckState(Search.KeywordFolder folder, CheckState checkState)
        {
            // Update all keywords in this folder
            foreach (var keyword in folder.Keywords)
            {
                keyword.KeywordEnabled = checkState;
            }

            // Recursively update all subfolders and their contents
            foreach (var childFolder in folder.Children)
            {
                CascadeFolderCheckState(childFolder, checkState);
            }

            // Refresh the TreeList to show the updated check states
            treeList1.RefreshDataSource();
        }

        #endregion

        #region Folder Operations

        private void NewFolder_ItemClick(object sender, EventArgs e)
        {
            // Create a new folder
            var newFolder = new Search.KeywordFolder
            {
                Name = "New Folder",
                Id = Guid.NewGuid().ToString()
            };

            // Add to the appropriate parent
            var focusedNode = treeList1.FocusedNode;
            if (focusedNode != null)
            {
                var dataRecord = treeList1.GetDataRecordByNode(focusedNode);
                if (dataRecord is Search.KeywordFolder parentFolder)
                {
                    newFolder.ParentFolder = parentFolder;
                    parentFolder.Children.Add(newFolder);
                }
                else
                {
                    // Add to root level
                    _ebaySearches.Folders.Add(newFolder);
                }
            }
            else
            {
                // Add to root level
                _ebaySearches.Folders.Add(newFolder);
            }

            treeList1.RefreshDataSource();
            treeList1.ExpandAll();
        }

        private void RenameFolder_ItemClick(object sender, EventArgs e)
        {
            var focusedNode = treeList1.FocusedNode;
            if (focusedNode != null && treeList1.GetDataRecordByNode(focusedNode) is Search.KeywordFolder folder)
            {
                // Start editing the folder name
                treeList1.FocusedColumn = treeList1.Columns["Alias"];
                treeList1.ShowEditor();
            }
        }

        private void DeleteFolder_ItemClick(object sender, EventArgs e)
        {
            var focusedNode = treeList1.FocusedNode;
            if (focusedNode != null && treeList1.GetDataRecordByNode(focusedNode) is Search.KeywordFolder folder)
            {
                var result = XtraMessageBox.Show(
                    $"Are you sure you want to delete the folder '{folder.Name}' and all its contents?",
                    "Delete Folder",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // Remove from parent
                    if (folder.ParentFolder != null)
                    {
                        folder.ParentFolder.Children.Remove(folder);
                    }
                    else
                    {
                        _ebaySearches.Folders.Remove(folder);
                    }

                    treeList1.RefreshDataSource();
                }
            }
        }

        #endregion
    }
}
