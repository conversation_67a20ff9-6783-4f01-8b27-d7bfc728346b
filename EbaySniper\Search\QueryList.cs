﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Xml;
using System.Xml.Schema;
using System.Xml.Serialization;
using DevExpress.XtraTreeList;

namespace uBuyFirst.Search
{
    [Serializable]
    [Obfuscation(Exclude = true)]
    [XmlInclude(typeof(KeywordFolder))]
    public class QueryList : TreeList.IVirtualTreeListData, IXmlSerializable
    {
        public QueryList()
        {
        }

        #region Properties

        /// <summary>
        /// Root-level folders
        /// </summary>
        public readonly List<KeywordFolder> Folders = new List<KeywordFolder>();

        /// <summary>
        /// Backward compatibility property - returns all keywords from all folders
        /// </summary>
        [XmlIgnore]
        public List<Keyword2Find> ChildrenCore
        {
            get { return GetAllKeywords(); }
        }

        #endregion

        #region TreeList Integration

        /// <summary>
        /// Provides root nodes for TreeList (folders)
        /// </summary>
        public void VirtualTreeGetChildNodes(VirtualTreeGetChildNodesInfo info)
        {
            info.Children = Folders;
        }

        public void VirtualTreeGetCellValue(VirtualTreeGetCellValueInfo info)
        {
            // Not used at root level
        }

        public void VirtualTreeSetCellValue(VirtualTreeSetCellValueInfo info)
        {
            // Not used at root level
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Gets all keywords from all folders
        /// </summary>
        private List<Keyword2Find> GetAllKeywords()
        {
            var allKeywords = new List<Keyword2Find>();

            foreach (var folder in Folders)
            {
                allKeywords.AddRange(folder.GetAllKeywords());
            }

            return allKeywords;
        }

        /// <summary>
        /// Migrates from flat keyword structure to folder structure
        /// </summary>
        public void MigrateFromFlatStructure(List<Keyword2Find> existingKeywords)
        {
            if (!existingKeywords.Any()) return;

            // Find keywords that aren't in any folder yet
            var orphanedKeywords = existingKeywords.Where(kw => kw.ParentFolder == null).ToList();

            if (!orphanedKeywords.Any()) return;

            // Find or create default folder
            var defaultFolder = Folders.FirstOrDefault(f => f.Name == "Default");
            if (defaultFolder == null)
            {
                defaultFolder = new KeywordFolder
                {
                    Name = "Default",
                    Id = "default-folder"
                };
                Folders.Add(defaultFolder);
            }

            // Move orphaned keywords to default folder
            foreach (var keyword in orphanedKeywords)
            {
                keyword.ParentFolder = defaultFolder;
                if (!defaultFolder.Keywords.Contains(keyword))
                {
                    defaultFolder.Keywords.Add(keyword);
                }
            }
        }

        /// <summary>
        /// Finds a folder by its full path
        /// </summary>
        public KeywordFolder FindFolderByPath(string folderPath)
        {
            if (string.IsNullOrEmpty(folderPath)) return null;

            var pathParts = folderPath.Split(new[] { " > " }, StringSplitOptions.RemoveEmptyEntries);

            foreach (var rootFolder in Folders)
            {
                var found = FindFolderByPathRecursive(rootFolder, pathParts, 0);
                if (found != null) return found;
            }

            return null;
        }

        private KeywordFolder FindFolderByPathRecursive(KeywordFolder folder, string[] pathParts, int index)
        {
            if (index >= pathParts.Length) return null;

            if (folder.Name.Equals(pathParts[index], StringComparison.OrdinalIgnoreCase))
            {
                if (index == pathParts.Length - 1) return folder; // Found target folder

                // Continue searching in children
                foreach (var child in folder.Children)
                {
                    var found = FindFolderByPathRecursive(child, pathParts, index + 1);
                    if (found != null) return found;
                }
            }

            return null;
        }

        #endregion

        #region XML Serialization

        public XmlSchema GetSchema()
        {
            return null;
        }

        public void ReadXml(XmlReader reader)
        {
            // Implementation for reading XML
        }

        public void WriteXml(XmlWriter writer)
        {
            // Implementation for writing XML
        }

        public IEnumerator GetEnumerator()
        {
            throw new NotImplementedException();
        }

        #endregion
    }
}