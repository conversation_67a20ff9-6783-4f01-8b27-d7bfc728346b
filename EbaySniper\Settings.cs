﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Media;
using System.Windows.Forms;
using System.Xml.Serialization;
using DevExpress.Data.Filtering.Helpers;
using DevExpress.LookAndFeel;
using DevExpress.Utils.Extensions;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraGrid.Views.BandedGrid;
using Microsoft.Win32;
using PushbulletSharp;
using uBuyFirst.AI;
using uBuyFirst.Filters;
using uBuyFirst.Grid;
using uBuyFirst.GUI;
using uBuyFirst.Item;
using uBuyFirst.Other;
using uBuyFirst.Prefs;
using uBuyFirst.Properties;
using uBuyFirst.Purchasing;
using uBuyFirst.Purchasing.Cookies;
using uBuyFirst.Search;
using uBuyFirst.Tools;
using uBuyFirst.Views;
using Settings = uBuyFirst.Tools.Settings;

namespace uBuyFirst
{
    public partial class Form1
    {
        private void readconfigs()
        {
            var files = Directory.GetFiles(@"C:\Temp\configs.2015.7.17", "*cfg");
            foreach (var file in files)
            {
                var ser = new XmlSerializer(typeof(Settings));
                TextReader reader = new StreamReader(Path.Combine(file));
                var settings = ((Settings)ser.Deserialize(reader));
                var list = Serializator.Stream2Object<List<Keyword2Find>>(settings.ListboxKeywords);
                foreach (var keyword2Find in list)
                {
                    File.AppendAllText(@"C:\Temp\configs.2015.7.17\0.csv",
                        $"{keyword2Find.Alias}\t{keyword2Find.Kws}\n");
                }
            }
        }

        private void LoadSettings(string configPath)
        {
            //Init before reading user config
            UserSettings.Highlightsvalues.Words1 = new string[] { };
            UserSettings.Highlightsvalues.Words2 = new string[] { };
            UserSettings.Highlightsvalues.Words3 = new string[] { };
            UserSettings.BrowserFontSize = 100;
            UserSettings.OpenInBrowser = UserSettings.OpenInBrowserEnum.Disabled;
            UserSettings.MakeOfferMessages = new SerializableDictionary<string, string>();
            repositoryItemComboBoxTimeZone.Items.AddRange(TimeZoneInfo.GetSystemTimeZones());
            UserSettings.CurrentTimeZoneInfo = TimeZoneInfo.Local;
            barEditItemTimeZone.EditValue = UserSettings.CurrentTimeZoneInfo.BaseUtcOffset.TotalHours;

            _ebaySearches = new QueryList();
            FilterActionFactory.Initialize(null);
            treeList1.DataSource = _ebaySearches;

            Intl.CountryProvider.SetTwoLetterToRss2Code();

            AlertOptionsClass.AlertOptions = new AlertOptionsClass();
            AlertOptionsClass.AlertControl1 = alertControl1;

            if (File.Exists(configPath))
            {
                LoadConfigFile(configPath);
            }
            else
            {
                Program.FirstVisit = true;
                //config.cfg not found

                /*if (settings.ViewsList != null)
                    ResultsView.InitViewDict(settings.ViewsList);

                RestoreEbaySearchesFromSettings(settings);
                ResultsView.FixMissingViewNameFromSearches(_ebaySearches.ChildrenCore);
                ResultsView.CreateViewsPanels();
                ResultsView.AssignViewsToSearches(_ebaySearches.ChildrenCore);
                */
                AssignDefaultShortcuts();
                var settingsViewsList = new List<string> { "Results" };
                ResultsView.InitViewDict(settingsViewsList);
                CreateDefaultSearches();

                ResultsView.CreateMissingViews(_ebaySearches.ChildrenCore);
                ResultsView.CreateViewsPanels();
                SetFiltersOnStartup(
                    "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");

                ResultsView.AssignViewsToSearches(_ebaySearches.ChildrenCore);
            }
            ResetGridViewIfNoWorkspacesFound();
            var uniqGrids = GridBuilder.GetUniqGrids(_ebaySearches.ChildrenCore);
            foreach (var grView in uniqGrids)
            {
                GridBuilder.ApplyPersistentColumnAppearance(grView);
                FormatRuleManager.ApplyGridFormatRules(grView);
            }

            //Order doesn't matter
            barWorkspaceMenuItem.WorkspacesPath = Folders.Workspace;
            GetRunOnStartupSetting();
            AlertOptionsClass.AlertOptions.SetAlertControlSettings(alertControl1);

            SetSubsearchFilterSetting();

            SetDevelopersSettings();
        }

        private void LoadConfigFile(string configPath)
        {
            try
            {
                var settings = Serializator.DeserializeSettings(configPath);
                ItemSpecifics.CategorySpecificsList.Clear();
                if (false)
                    AiAnalysis.AiColumnsList.Clear();
                if (settings.CategorySpecifics != null)
                {
                    foreach (var c in settings.CategorySpecifics)
                    {
                        if (c.ToString() != "")
                        {
                            var defaultDataTable = GridBuilder.GetDefaultDataTable();
                            if (defaultDataTable.Columns.Contains(c.ToString()))
                                continue;

                            ItemSpecifics.CategorySpecificsList.Add(c);
                        }
                    }
                }

                AiAnalysis.AiColumnsList.Clear();
                if (settings.AiColumnsList != null)
                {
                    foreach (var c in settings.AiColumnsList)
                    {
                        if (c.ToString() != "")
                        {
                            var defaultDataTable = GridBuilder.GetDefaultDataTable();
                            if (defaultDataTable.Columns.Contains(c.ToString()))
                                continue;
                            if (!AiAnalysis.AiColumnsList.Contains(c))
                                AiAnalysis.AiColumnsList.Add(c);
                        }
                    }
                }
                //backup need datasourcerefresh?

                //Remove existing gridviews
                var j = 0;
                var gridViewNames = ResultsView.ViewsDict.Keys.ToList();
                while (j < gridViewNames.Count)
                {
                    var obsoletePanel = dockManager1.Panels.FirstOrDefault(p => p.Tag?.ToString() == gridViewNames[j]);
                    obsoletePanel?.Dispose();
                    ResultsView.RemoveFromViewList(gridViewNames[j]);
                    j++;
                }

                if (settings.ViewsList != null)
                    ResultsView.InitViewDict(settings.ViewsList);

                //backup need datasourcerefresh?
                _ebaySearches.ChildrenCore.Clear();
                RestoreEbaySearchesFromSettings(settings);
                ResultsView.CreateMissingViews(_ebaySearches.ChildrenCore);
                ResultsView.CreateViewsPanels();
                ResultsView.AssignViewsToSearches(_ebaySearches.ChildrenCore);

                EBayAccountsList.Clear();
                foreach (var ebayAccount in settings.TList)
                {
                    EBayAccountsList.Add(ebayAccount);
                }

                CreateDefaultSearches();
                //Apply DGV1 settings
                //Gridview

                if (settings.RowHeight > 22)
                {
                    UserSettings.GridRowHeight = settings.RowHeight;
                    barEditItemRowHeight.EditValue = settings.RowHeight;
                }
                else
                {
                    UserSettings.GridRowHeight = 22;
                    barEditItemRowHeight.EditValue = 22;
                }

                var fontDialog1Font = StringToFont(settings.GridViewFont);
                fontDialog1.Font = fontDialog1Font;
                UserSettings.GridViewFont = fontDialog1.Font;
                SetGridViewFont(fontDialog1.Font);

                //backup need datasourcerefresh?
                lstchkXfilterList.Items.Clear();
                XFilterManager.ClearFilters();
                if (!string.IsNullOrEmpty(settings.XFiltersList))
                {
                    SetFiltersOnStartup(settings.XFiltersList);
                }

                //Other settings
                //Default == false
                barCheckItemSoundAlert.Checked = settings.SoundAlert;

                barCheckItemMaximizewindow.Checked = settings.Maximize;
                UserSettings.OpenInBrowser = settings.OpenInBrowser;
                barCheckItemTotalPriceAsMaxPrice.Checked = settings.TotalPriceAsMaxPrice;
                barCheckItemAutoStartSearch.Checked = settings.AutoStartSearch;
                barEditItemAutoSelect.EditValue = settings.FocusTopRowTimeout;

                barIdleTimeoutMinutes.EditValue = settings.IdleTime;
                SearchService.IdleTimeMaximum = settings.IdleTime;
                barEnableIdleTimeout.Checked = settings.IdleChkBoxChecked;
                SearchService.IdleTimeEnabled = settings.IdleChkBoxChecked;
                barIdleTimeoutMinutes.Enabled = barEnableIdleTimeout.Checked;
                //chkLargeImagesOnHover.Checked = settings.LoadLargeImagesOnHover;

                CreditCardService.CreditCardPaymentEnabled = settings.WindowMaximum == 7331;
                UserSettings.SkipBuyConfirmation = settings.WindowMinimum == 1337;
                UserSettings.RestockerEnabled = settings.PanelMaximum == 2337;
                CookieManager.Profile = settings.WindowProfile;

                _myPlayer = new SoundPlayer { Stream = Resources.binbloop };
                if (!string.IsNullOrEmpty(settings.SoundLocation) && File.Exists(settings.SoundLocation))
                    _myPlayer.SoundLocation = settings.SoundLocation;

                //Skin
                UserLookAndFeel.Default.SkinName = "Basic";
                if (!string.IsNullOrEmpty(settings.Skin))
                    UserLookAndFeel.Default.SkinName = settings.Skin;

                //Browser
                SetBrowserHighlighting(settings);
                LoadShortcuts(settings);
                UserSettings.BrowserBg = !string.IsNullOrEmpty(settings.BrowserBg) ? settings.BrowserBg : "#ffffff";
                if (settings.Fontsize > 0)
                {
                    UserSettings.BrowserFontSize = settings.Fontsize;
                    zoomTrackBarBrowser.Value = UserSettings.BrowserFontSize;
                }

                //Gallery
                if (settings.ZoomBarPics > 0)
                    zoomTrackBarPictures.Value = settings.ZoomBarPics;

                // This handles case where these aren't in the config file yet.
                if (settings.ZoomBarPicsLarge <= 0)
                    settings.ZoomBarPicsLarge = settings.ZoomBarPics;
                if (settings.ZoomBarPicsSmall <= 0)
                    settings.ZoomBarPicsSmall = settings.ZoomBarPics;

                // Large one goes from 1..100
                if (settings.ZoomBarPicsLarge > 0)
                {
                    if (settings.ZoomBarPicsLarge > 100)
                    {
                        settings.ZoomBarPicsLarge = 100;
                    }
                    zoomTrackBarExpanded.Value = settings.ZoomBarPicsLarge;
                }
                if (settings.ZoomBarPicsSmall > 0)
                {
                    zoomTrackBarPictures.Value = settings.ZoomBarPicsSmall;
                }

                // Ensure last value for UI is set so gui will start with the last setting
                if (settings.ZoomBarPicsSmall > 0)
                    LastSliderValueSmall = settings.ZoomBarPicsSmall;
                if (settings.ZoomBarPicsLarge > 0)
                    LastSliderValueLarge = settings.ZoomBarPicsLarge;


                galleryControl1.Gallery.Groups.Last().Gallery.ImageSize = new Size(zoomTrackBarPictures.Value, zoomTrackBarPictures.Value);

                //Timezone
                if (!string.IsNullOrEmpty(settings.TimeZoneID))
                {
                    UserSettings.CurrentTimeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(settings.TimeZoneID);
                    barEditItemTimeZone.EditValue = UserSettings.CurrentTimeZoneInfo.BaseUtcOffset.TotalHours;
                }

                //Pushbullet
                _pushbullet?.Dispose();
                _pushbullet = null;
                barCheckItemPushBullet.Checked = false;
                if (settings.Pushbullet != null)
                {
                    _pushbullet = settings.Pushbullet;
                    if (!string.IsNullOrEmpty(_pushbullet.Token))
                    {
                        _pushbullet.PbClient = new PushbulletClient(_pushbullet.Token);
                        barCheckItemPushBullet.Checked = _pushbullet.Enabled;
                    }
                }
                //Telegram
                _telegramSender?.StopListening();
                _telegramSender = null;

                if (settings.TelegramSender != null)
                {
                    _telegramSender = settings.TelegramSender;
                    if (!string.IsNullOrEmpty(_telegramSender.TelegramBotID))
                    {
                        try
                        {
                            _telegramSender.CreateTelegramBotClient(_telegramSender.TelegramBotID);
                        }
                        catch (Exception exception)
                        {
                            XtraMessageBox.Show("Telegram: " + exception.Message);
                        }
                    }
                }
                FilterActionFactory.Initialize(_telegramSender);

                //Alert
                AlertOptionsClass.AlertOptions = new AlertOptionsClass();
                if (settings.AlertSettings != null)
                    AlertOptionsClass.AlertOptions = settings.AlertSettings;

                Placeoffer.PurchaseAllQuantity = settings.QuickPurchaseAllQuantity;
                Placeoffer.BestOfferSubtractShipping = settings.BestOfferSubtractShipping;

                if (settings.RequestsConfig != null)
                {
                    SearchConfigManager.Instance.SetConfig(settings.RequestsConfig);
                }

                if (settings.MakeOfferMessages != null)
                {
                    UserSettings.MakeOfferMessages = settings.MakeOfferMessages;
                    var messages = UserSettings.MakeOfferMessages.Keys.ToList();
                    for (var i = 0; i < messages.Count; i++)
                    {
                        UserSettings.MakeOfferMessages[messages[i]] = UserSettings.MakeOfferMessages[messages[i]].Replace("\n", "\r\n");
                    }

                    UserSettings.MakeOfferSelectedMessage = settings.MakeOfferSelectedMessage;
                }

                UserSettings.InitialResultsLimit = settings.InitialResultsLimit ?? 20;
                barEditItemInitialResultsLimit.EditValue = UserSettings.InitialResultsLimit;

                UserSettings.MaxResultsCount = settings.MaxResultsCount < 10 ? 500 : settings.MaxResultsCount;
                barEditItemResultsMaxCount.EditValue = UserSettings.MaxResultsCount;

                UserSettings.ClickOnPriceOpensProductPage = settings.ClickOnPriceOpensProductPage;
                if (settings.WindowMaximized)
                    WindowState = FormWindowState.Maximized;

                if (settings.BlockedSellers != null)
                    UserSettings.BlockedSellers = new HashSet<string>(settings.BlockedSellers);
                else
                    UserSettings.BlockedSellers = new HashSet<string>();
                if (settings.IgnoredSellers != null)
                {
                    UserSettings.BlockedSellers.AddRange(settings.IgnoredSellers);
                    settings.IgnoredSellers = null;
                }
                UserSettings.ClickOnPriceOpensProductPage = settings.ClickOnPriceOpensProductPage;
                barCheckItemPriceOpensCheckout.Checked = !UserSettings.ClickOnPriceOpensProductPage;
                UserSettings.ExternalDataEnabled = settings.ExternalDataEnabled;
                UserSettings.SyncSearchTermsEnabled = settings.SyncSearchTermsEnabled;
                UserSettings.SyncSearchTermsUrl = settings.SyncSearchTermsUrl;
                if (settings.SyncSearchTermsInterval == 0)
                    settings.SyncSearchTermsInterval = 3600;
                UserSettings.SyncSearchTermsInterval = settings.SyncSearchTermsInterval;
                UserSettings.SyncSearchTermsFileHash = settings.SyncSearchTermsFileHash;
                UserSettings.ExternalEndpointUrl = settings.ExternalEndpointUrl ?? "https://www.amazon.com/s?k={Title}";
                UserSettings.AiExternalEndpointUrl = settings.AiExternalEndpointUrl ?? "http://localhost:8000/match_mydata?Title={Title}";
                UserSettings.InternalEndpointUrl = settings.InternalEndpointUrl ?? "http://localhost:8000/match_mydata";
                if (string.IsNullOrEmpty(settings.InternalEndpointUrl))
                {
                    UserSettings.InternalEndpointUrl = "http://localhost:8000/match_mydata";
                }
                UserSettings.IsInternalEndpoint = settings.IsInternalEndpoint;
                UserSettings.IsAIEndpoint = settings.IsAIEndpoint;
                UserSettings.AiProviderIndex = settings.AiProviderIndex;
                UserSettings.AiProviderModelIndex = settings.AiProviderModelIndex;
                UserSettings.SendDescriptionAndPictures = settings.SendDescriptionAndPictures;
                ProgramState.UBuyFirstRedirectTimestamp = settings.UBuyFirstRedirectTimestamp;

                // Load daily spend limit settings
                UserSettings.DailySpendLimit = settings.DailyLimit;
                LoadDailySpendHistory(settings.DailyHistoryJson);

                var sellerFile = Path.Combine(Folders.Settings, "cache.bin");
                if (File.Exists(sellerFile))
                {
                    try
                    {
                        var serializedSellers = File.ReadAllText(sellerFile);
                        var deserializedCache = Serializator.DeserializeConcurrentDictionary<string, string>(serializedSellers);
                        _sellerCacheService.Clear();
                        foreach (var kvp in deserializedCache)
                        {
                            _sellerCacheService.Set(kvp.Key, kvp.Value);
                        }
                    }
                    catch (Exception e)
                    {
                        // Log error or handle appropriately
                        Debug.WriteLine($"Error loading seller cache: {e.Message}");
                    }
                }

                chkRefreshAndNotifyWatchlist.IsOn = settings.WatchlistAutoRefreshEnabled;
                var seconds = settings.WatchlistRefreshInterval > 0 ? settings.WatchlistRefreshInterval : 300;
                timeSpanWatchlistRefreshInterval.EditValue = TimeSpan.FromSeconds(seconds);

                if (_watchlistManager != null)
                {
                    _watchlistManager.ClearItems();
                    _watchlistManager.SetRefreshInterval(TimeSpan.FromSeconds(seconds));
                    _watchlistManager.SetAutoRefresh(settings.WatchlistAutoRefreshEnabled); // Use FirstOrDefault() for the account
                }
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("Reading config.cfg: ", ex);
                MessageBox.Show(string.Format(En_US.Form1_LoadSettings_, ex.Message), "Settings:");
            }
        }

        private void LoadShortcuts(Settings settings)
        {
            AssignDefaultShortcuts();

            UserSettings.Shortcuts.BuyKey = settings.ShortcutBuyKey;
            UserSettings.Shortcuts.BuyModifier = settings.ShortcutBuyModifier;
            UserSettings.Shortcuts.GoToCheckoutKey = settings.ShortcutGoToCheckoutKey;
            UserSettings.Shortcuts.GoToCheckoutModifier = settings.ShortcutGoToCheckoutModifier;
            UserSettings.Shortcuts.QuickBuyKey = settings.ShortcutQuickBuyKey;
            UserSettings.Shortcuts.QuickBuyModifier = settings.ShortcutQuickBuyModifier;
            UserSettings.Shortcuts.ImmediateBuyKey = settings.ShortcutImmediateBuyKey;
            UserSettings.Shortcuts.ImmediateBuyModifier = settings.ShortcutImmediateBuyModifier;
            UserSettings.Shortcuts.MakeOfferKey = settings.ShortcutMakeOfferKey;
            UserSettings.Shortcuts.MakeOfferModifier = settings.ShortcutMakeOfferModifier;
        }

        /// <summary>
        /// Loads daily spend history from JSON string
        /// </summary>
        private void LoadDailySpendHistory(string json)
        {
            try
            {
                if (string.IsNullOrEmpty(json))
                {
                    UserSettings.DailySpendHistory = new Dictionary<DateTime, decimal>();
                    return;
                }

                // Deserialize from JSON with custom date handling
                var jsonSettings = new Newtonsoft.Json.JsonSerializerSettings
                {
                    DateFormatString = "yyyy-MM-dd"
                };

                var dictionary = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string, decimal>>(json, jsonSettings);
                UserSettings.DailySpendHistory = new Dictionary<DateTime, decimal>();

                if (dictionary != null)
                {
                    foreach (var kvp in dictionary)
                    {
                        if (DateTime.TryParseExact(kvp.Key, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out var date))
                        {
                            UserSettings.DailySpendHistory[date] = kvp.Value;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Form1.Log?.Error(ex, "Failed to load daily spend history, initializing empty dictionary");
                UserSettings.DailySpendHistory = new Dictionary<DateTime, decimal>();
            }
        }

        /// <summary>
        /// Serializes daily spend history to JSON string
        /// </summary>
        private string SerializeDailySpendHistory()
        {
            try
            {
                if (UserSettings.DailySpendHistory == null)
                    return "{}";

                // Convert DateTime keys to string format for JSON serialization
                var stringDictionary = new Dictionary<string, decimal>();
                foreach (var kvp in UserSettings.DailySpendHistory)
                {
                    stringDictionary[kvp.Key.ToString("yyyy-MM-dd")] = kvp.Value;
                }

                var jsonSettings = new Newtonsoft.Json.JsonSerializerSettings
                {
                    Formatting = Newtonsoft.Json.Formatting.None
                };

                return Newtonsoft.Json.JsonConvert.SerializeObject(stringDictionary, jsonSettings);
            }
            catch (Exception ex)
            {
                Form1.Log?.Error(ex, "Failed to serialize daily spend history");
                return "{}";
            }
        }

        private static void AssignDefaultShortcuts()
        {
            UserSettings.Shortcuts.BuyKey = (int)Keys.Enter;
            UserSettings.Shortcuts.BuyModifier = (int)Keys.None;
            UserSettings.Shortcuts.GoToCheckoutKey = (int)Keys.Enter;
            UserSettings.Shortcuts.GoToCheckoutModifier = (int)Keys.Shift;
            UserSettings.Shortcuts.QuickBuyKey = (int)Keys.Return;
            UserSettings.Shortcuts.QuickBuyModifier = (int)Keys.Control;
            UserSettings.Shortcuts.ImmediateBuyKey = (int)Keys.None;
            UserSettings.Shortcuts.ImmediateBuyModifier = (int)Keys.None;
            UserSettings.Shortcuts.MakeOfferKey = (int)Keys.Oemtilde;
            UserSettings.Shortcuts.MakeOfferModifier = (int)Keys.None;
        }

        private static void SetGridViewFont(Font font)
        {
            foreach (var gridControl in ResultsView.ViewsDict.Values)
            {
                try
                {
                    var grView = (AdvBandedGridView)gridControl.MainView;
                    grView.Appearance.Row.Font = font;
                    grView.Appearance.FocusedRow.Font = font;
                    grView.Appearance.SelectedRow.Font = font;
                }
                catch (Exception e)
                {
                    Debug.WriteLine(e);
                }
            }
        }

        private static Font StringToFont(string fontStr)
        {
            if (string.IsNullOrEmpty(fontStr))
                return new Font("Microsoft Sans Serif", 8.25f, FontStyle.Regular);

            var converter = TypeDescriptor.GetConverter(typeof(Font));
            try
            {
                if (!(converter.ConvertFromString(fontStr) is Font font))
                    return new Font("Microsoft Sans Serif", 8.25f, FontStyle.Regular);

                return font;
            }
            catch (Exception e)
            {
                Debug.WriteLine(e);
            }
            return new Font("Microsoft Sans Serif", 8.25f, FontStyle.Regular);
        }

        private void SetSubsearchFilterSetting()
        {
            filterControlTerm.SourceControl = _ebaySearches.ChildrenCore.FirstOrDefault()?.GridControl.DataSource;
            var colCondition = filterControlTerm.FilterColumns.GetFilterColumnByCaption("Condition");
            if (colCondition != null)
            {
                filterControlTerm.FilterColumns.Remove(colCondition);
                var ufc = new CustomFilterColumn("Condition", "Condition", typeof(string), new RepositoryItemTextEdit(), FilterColumnClauseClass.String);
                filterControlTerm.FilterColumns.Add(ufc);
            }
        }

        private void SetDevelopersSettings()
        {
            var config = SearchConfigManager.Instance;

            spinEditFindReqMaxThreads.EditValue = config.FindReqMaxThreads;
            spinEditGetItemDetailsMaxThreads.EditValue = config.GetItemDetailsReqMaxThreads;
            chkDownloadAvatars.Checked = config.DownloadAvatars;
            chkDownloadOtherImages.Checked = config.DownloadOtherImages;
            chkDownloadDescription.Checked = config.DownloadDescription;
            chkUpdateItemStatusFor2Min.Checked = config.UpdateItemStatus;
            checkUseAPI.Checked = config.EnabledApi;
            checkUseRSS.Checked = config.EnabledRss;
            checkUseRSS2.Checked = config.EnabledRss2;
            checkEditShowSoldItems.Checked = config.ShowSoldItems;
            checkEditWhitespaceTokenizer.Checked = config.WhiteSpaceAnalyzer;
            SetWhiteSpaceAnalyzer(config.WhiteSpaceAnalyzer);
        }

        private static void SetBrowserHighlighting(Settings settings)
        {
            // Set default values for colors and hex codes
            SetHighlightColor(ref UserSettings.Highlightsvalues.Color1, ref UserSettings.Highlightsvalues.HexColor1, settings.HighlightColor1);
            SetHighlightColor(ref UserSettings.Highlightsvalues.Color2, ref UserSettings.Highlightsvalues.HexColor2, settings.HighlightColor2);
            SetHighlightColor(ref UserSettings.Highlightsvalues.Color3, ref UserSettings.Highlightsvalues.HexColor3, settings.HighlightColor3);

            // Set words highlights
            UserSettings.Highlightsvalues.Words1 = FilterValidWords(settings.WordsHighlight1);
            UserSettings.Highlightsvalues.Words2 = FilterValidWords(settings.WordsHighlight2);
            UserSettings.Highlightsvalues.Words3 = FilterValidWords(settings.WordsHighlight3);
        }

        private static void SetHighlightColor(ref Color colorField, ref string hexField, int highlightColor)
        {
            if (highlightColor != 0)
            {
                colorField = Color.FromArgb(highlightColor);
                hexField = WebView.HexConverter(colorField);
            }
            else
            {
                colorField = Color.FromArgb(0, 0, 0, 0); // Default transparent color
                hexField = null;
            }
        }

        private static string[] FilterValidWords(IEnumerable<string> words)
        {
            return words?.Where(word => !string.IsNullOrWhiteSpace(word)).ToArray() ?? Array.Empty<string>();
        }

        private void RestoreEbaySearchesFromSettings(Settings settings)
        {
            if (settings.ListboxKeywords == null)
                return;

            treeList1.BeginUpdate();

            try
            {
                // Try to deserialize as QueryList first (new format)
                var queryList = Serializator.Stream2Object<Search.QueryList>(settings.ListboxKeywords);
                if (queryList != null && queryList.Folders.Any())
                {
                    // New format with folder structure - already complete from XML deserialization
                    _ebaySearches.Folders.Clear();
                    _ebaySearches.Folders.AddRange(queryList.Folders);

                    // Only restore parent relationships, keywords are already in folders
                    RestoreParentRelationships(_ebaySearches.Folders, null);
                }
                else
                {
                    // Fall back to old format (List<Keyword2Find>)
                    var keyword2FindList = Serializator.Stream2Object<List<Keyword2Find>>(settings.ListboxKeywords);
                    if (keyword2FindList != null && keyword2FindList.Any())
                    {
                        RestoreFromLegacyFormat(keyword2FindList);
                    }
                }
            }
            catch
            {
                // If QueryList deserialization fails, try legacy format
                try
                {
                    var keyword2FindList = Serializator.Stream2Object<List<Keyword2Find>>(settings.ListboxKeywords);
                    if (keyword2FindList != null && keyword2FindList.Any())
                    {
                        RestoreFromLegacyFormat(keyword2FindList);
                    }
                }
                catch
                {
                    // If both fail, just continue with empty structure
                }
            }

            treeList1.RefreshDataSource();
            treeList1.EndUpdate();
        }

        private void RestoreParentRelationships(List<Search.KeywordFolder> folders, Search.KeywordFolder parentFolder)
        {
            foreach (var folder in folders)
            {
                folder.ParentFolder = parentFolder;

                // Setup keywords in this folder - only set parent relationships, don't add to folder again
                foreach (var keyword in folder.Keywords)
                {
                    SetupKeywordParentRelationships(keyword, folder);
                }

                // Recursively setup child folders
                if (folder.Children.Any())
                {
                    RestoreParentRelationships(folder.Children, folder);
                }
            }
        }

        private void RestoreFolderHierarchy(List<Search.KeywordFolder> folders, Search.KeywordFolder parentFolder)
        {
            foreach (var folder in folders)
            {
                folder.ParentFolder = parentFolder;

                // Setup keywords in this folder
                foreach (var keyword in folder.Keywords)
                {
                    SetupKeyword(keyword, folder);
                }

                // Recursively setup child folders
                if (folder.Children.Any())
                {
                    RestoreFolderHierarchy(folder.Children, folder);
                }
            }
        }

        private void RestoreFromLegacyFormat(List<Keyword2Find> keyword2FindList)
        {
            // Group keywords by their FolderPath property
            var keywordsByFolder = keyword2FindList.GroupBy(k => k.FolderPath ?? "").ToList();

            foreach (var group in keywordsByFolder)
            {
                var folderPath = group.Key;
                var keywords = group.ToList();

                Search.KeywordFolder targetFolder;

                if (string.IsNullOrEmpty(folderPath))
                {
                    // Keywords without folder path go to default folder
                    targetFolder = _ebaySearches.Folders.FirstOrDefault(f => f.Name == "Default");
                    if (targetFolder == null)
                    {
                        targetFolder = new Search.KeywordFolder
                        {
                            Name = "Default",
                            Id = "default-folder"
                        };
                        _ebaySearches.Folders.Add(targetFolder);
                    }
                }
                else
                {
                    // Find or create folder by path
                    targetFolder = _ebaySearches.FindFolderByPath(folderPath);
                    if (targetFolder == null)
                    {
                        targetFolder = CreateFolderByPath(folderPath);
                    }
                }

                // Add keywords to the target folder
                foreach (var keyword in keywords)
                {
                    SetupKeyword(keyword, targetFolder);
                    if (!targetFolder.Keywords.Contains(keyword))
                    {
                        targetFolder.Keywords.Add(keyword);
                    }
                }
            }
        }

        private Search.KeywordFolder CreateFolderByPath(string folderPath)
        {
            var pathParts = folderPath.Split(new[] { " > " }, StringSplitOptions.RemoveEmptyEntries);
            Search.KeywordFolder currentFolder = null;
            Search.KeywordFolder parentFolder = null;

            for (int i = 0; i < pathParts.Length; i++)
            {
                var folderName = pathParts[i].Trim();
                var partialPath = string.Join(" > ", pathParts.Take(i + 1));

                currentFolder = _ebaySearches.FindFolderByPath(partialPath);
                if (currentFolder == null)
                {
                    currentFolder = new Search.KeywordFolder
                    {
                        Name = folderName,
                        Id = Guid.NewGuid().ToString(),
                        ParentFolder = parentFolder
                    };

                    if (parentFolder != null)
                    {
                        parentFolder.Children.Add(currentFolder);
                    }
                    else
                    {
                        _ebaySearches.Folders.Add(currentFolder);
                    }
                }

                parentFolder = currentFolder;
            }

            return currentFolder;
        }

        private void SetupKeywordParentRelationships(Keyword2Find keyword, Search.KeywordFolder parentFolder)
        {
            if (string.IsNullOrEmpty(keyword.ViewName))
                keyword.ViewName = "Results";

            keyword.EBaySite = Intl.CountryProvider.GetEbaySite(keyword.EbaySiteName);
            keyword.ParentCore = _ebaySearches;
            keyword.ParentFolder = parentFolder;

            foreach (var childTerm in keyword.ChildrenCore)
            {
                childTerm.SetParent(keyword);
                childTerm.SubSearch?.Rebuild();
            }
        }

        private void SetupKeyword(Keyword2Find keyword, Search.KeywordFolder parentFolder)
        {
            if (string.IsNullOrEmpty(keyword.ViewName))
                keyword.ViewName = "Results";

            keyword.EBaySite = Intl.CountryProvider.GetEbaySite(keyword.EbaySiteName);
            keyword.ParentCore = _ebaySearches;
            keyword.ParentFolder = parentFolder;

            foreach (var childTerm in keyword.ChildrenCore)
            {
                childTerm.SetParent(keyword);
                childTerm.SubSearch?.Rebuild();
            }
        }

        private void SetFiltersOnStartup(string xFiltersListStr)
        {
            try
            {
                XFilterManager.DeserializeXfilters(xFiltersListStr);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Loading filters : {ex.Message}");
            }

            lstchkXfilterList.DataSource = XFilterManager.GetDataSource();
            lstchkXfilterList.DisplayMember = "Alias";
            lstchkXfilterList.CheckMember = "Enabled";
            lstchkXfilterList.Refresh();
        }

        public void SaveSettings()
        {
            Settings.SettingsReadWriteLock.EnterWriteLock();
            Debug.WriteLine($"{DateTime.Now:s}\tWriting settings");
            try
            {
                // Serialize the entire QueryList to preserve folder structure
                var xml = new XmlSerializer(_ebaySearches.GetType());
                var keyword2FindStream = Serializator.Object2Stream(xml, _ebaySearches);

                var fontstr = "";
                var grView = (AdvBandedGridView)_ebaySearches.ChildrenCore.FirstOrDefault()?.GridControl.MainView;
                if (grView?.Appearance.Row.Font != null)
                {
                    var converter = TypeDescriptor.GetConverter(typeof(Font));
                    fontstr = converter.ConvertToString(grView.Appearance.Row.Font);
                }

                var settings = new Settings();
                settings.SoundAlert = barCheckItemSoundAlert.Checked;
                settings.SoundLocation = _myPlayer.SoundLocation;
                settings.Maximize = barCheckItemMaximizewindow.Checked;
                settings.IdleTime = Convert.ToDecimal(barIdleTimeoutMinutes.EditValue.ToString());
                settings.ListboxKeywords = keyword2FindStream.ToArray();
                settings.IdleChkBoxChecked = SearchService.IdleTimeEnabled;
                settings.HighlightColor1 = UserSettings.Highlightsvalues.Color1.ToArgb();
                settings.HighlightColor2 = UserSettings.Highlightsvalues.Color2.ToArgb();
                settings.HighlightColor3 = UserSettings.Highlightsvalues.Color3.ToArgb();
                settings.WordsHighlight1 = UserSettings.Highlightsvalues.Words1;
                settings.WordsHighlight2 = UserSettings.Highlightsvalues.Words2;
                settings.WordsHighlight3 = UserSettings.Highlightsvalues.Words3;
                settings.Fontsize = UserSettings.BrowserFontSize;
                settings.CategorySpecifics = ItemSpecifics.CategorySpecificsList;
                settings.AiColumnsList = AiAnalysis.AiColumnsList;
                settings.RowHeight = decimal.TryParse(barEditItemRowHeight.EditValue?.ToString(), out var height) ? height : 22m;
                settings.BrowserBg = UserSettings.BrowserBg;
                settings.TList = EBayAccountsList.ToList();
                settings.XFiltersList = XFilterManager.GetSerializedDatasource();
                settings.AutoStartSearch = barCheckItemAutoStartSearch.Checked;
                settings.FocusTopRowTimeout = (decimal)barEditItemAutoSelect.EditValue;
                settings.ZoomBarPics = zoomTrackBarPictures.Value;
                settings.ZoomBarPicsLarge = zoomTrackBarExpanded.Value;
                settings.ZoomBarPicsSmall = zoomTrackBarPictures.Value;
                settings.Skin = UserLookAndFeel.Default.SkinName;
                settings.TimeZoneID = UserSettings.CurrentTimeZoneInfo.Id;
                settings.Pushbullet = _pushbullet;
                settings.WatchlistAutoRefreshEnabled = chkRefreshAndNotifyWatchlist.IsOn;
                if (timeSpanWatchlistRefreshInterval.EditValue is TimeSpan currentInterval)
                    settings.WatchlistRefreshInterval = (int)currentInterval.TotalSeconds;
                settings.TelegramSender = _telegramSender;
                settings.AlertSettings = AlertOptionsClass.AlertOptions;
                settings.RequestsConfig = SearchConfigManager.Instance.GetConfigForSerialization();
                //settings.LoadLargeImagesOnHover = chkLargeImagesOnHover.Checked;
                settings.QuickPurchaseAllQuantity = Placeoffer.PurchaseAllQuantity;
                settings.BestOfferSubtractShipping = Placeoffer.BestOfferSubtractShipping;
                settings.GridViewFont = fontstr;
                settings.ViewsList = ResultsView.ViewsDict.Keys.ToList();
                settings.WindowMaximum = CreditCardService.CreditCardPaymentEnabled ? 7331 : 0;
                settings.WindowMinimum = UserSettings.SkipBuyConfirmation ? 1337 : 0;
                settings.PanelMaximum = UserSettings.SkipBuyConfirmation ? 2337 : 0;
                settings.WindowProfile = CookieManager.Profile;
                settings.OpenInBrowser = UserSettings.OpenInBrowser;
                settings.OpenInBrowser = UserSettings.OpenInBrowser;
                settings.MakeOfferMessages = UserSettings.MakeOfferMessages;
                settings.MakeOfferSelectedMessage = UserSettings.MakeOfferSelectedMessage;
                settings.InitialResultsLimit = UserSettings.InitialResultsLimit;
                settings.MaxResultsCount = UserSettings.MaxResultsCount;

                settings.ShortcutBuyKey = UserSettings.Shortcuts.BuyKey;
                settings.ShortcutGoToCheckoutKey = UserSettings.Shortcuts.GoToCheckoutKey;
                settings.ShortcutGoToCheckoutModifier = UserSettings.Shortcuts.GoToCheckoutModifier;
                settings.ShortcutQuickBuyKey = UserSettings.Shortcuts.QuickBuyKey;
                settings.ShortcutQuickBuyModifier = UserSettings.Shortcuts.QuickBuyModifier;
                settings.ShortcutImmediateBuyKey = UserSettings.Shortcuts.ImmediateBuyKey;
                settings.ShortcutImmediateBuyModifier = UserSettings.Shortcuts.ImmediateBuyModifier;
                settings.ShortcutMakeOfferKey = UserSettings.Shortcuts.MakeOfferKey;
                settings.ShortcutMakeOfferModifier = UserSettings.Shortcuts.MakeOfferModifier;
                settings.TotalPriceAsMaxPrice = barCheckItemTotalPriceAsMaxPrice.Checked;
                settings.ClickOnPriceOpensProductPage = UserSettings.ClickOnPriceOpensProductPage;
                settings.WindowMaximized = WindowState == FormWindowState.Maximized;
                settings.SyncSearchTermsEnabled = UserSettings.SyncSearchTermsEnabled;
                settings.SyncSearchTermsUrl = UserSettings.SyncSearchTermsUrl;
                settings.SyncSearchTermsInterval = UserSettings.SyncSearchTermsInterval;
                settings.SyncSearchTermsFileHash = UserSettings.SyncSearchTermsFileHash;
                settings.ExternalEndpointUrl = UserSettings.ExternalEndpointUrl;
                settings.AiExternalEndpointUrl = UserSettings.AiExternalEndpointUrl;
                settings.InternalEndpointUrl = UserSettings.InternalEndpointUrl;
                settings.IsInternalEndpoint = UserSettings.IsInternalEndpoint;
                settings.IsAIEndpoint = UserSettings.IsAIEndpoint;
                settings.AiProviderIndex = UserSettings.AiProviderIndex;
                settings.AiProviderModelIndex = UserSettings.AiProviderModelIndex;
                settings.ExternalDataEnabled = UserSettings.ExternalDataEnabled;
                settings.SendDescriptionAndPictures = UserSettings.SendDescriptionAndPictures;
                settings.UBuyFirstRedirectTimestamp = ProgramState.UBuyFirstRedirectTimestamp;

                // Save daily spend limit settings
                settings.DailyLimit = UserSettings.DailySpendLimit;
                settings.DailyHistoryJson = SerializeDailySpendHistory();

                if (UserSettings.BlockedSellers != null)
                {
                    settings.BlockedSellers = UserSettings.BlockedSellers.ToArray();
                }
                else
                {
                    settings.BlockedSellers = new string[] { };
                }

                var ser = new XmlSerializer(typeof(Settings));
                try
                {
                    var filePath = Path.Combine(Folders.Settings, "config.cfg");
                    using (Stream file = new FileStream(filePath, FileMode.Create, FileAccess.Write, FileShare.None, 0x1000, FileOptions.WriteThrough))
                    using (TextWriter writer = new StreamWriter(file))
                    {
                        ser.Serialize(writer, settings);
                    }
                }
                catch (Exception ex)
                {
                    Log.Error("{0}, {1} Exception:{2}", ProgramState.UBFVersion, ProgramState.HWID + ProgramState.SerialNumber, ex);
                }

                BackupCfg(settings);
                RemoveNonDefaultWorkspaces();
                SaveUserWorkspaces();

                var sellersFile = Path.Combine(Folders.Settings, "cache.bin");
                // Save only if the cache service is available and enough time has passed
                if ((DateTime.UtcNow - File.GetLastWriteTimeUtc(sellersFile)).TotalSeconds > 10)
                {
                    // Serialize data from the injected cache service
                    var cacheEntries = _sellerCacheService.GetAllEntries();
                    // Convert IEnumerable back to ConcurrentDictionary for serialization
                    var concurrentCacheData = new System.Collections.Concurrent.ConcurrentDictionary<string, string>(cacheEntries);
                    var serializedSeller = Serializator.SerializeConcurrentDictionary(concurrentCacheData);
                    File.WriteAllText(sellersFile, serializedSeller);
                }
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("Saving settings ", ex);
            }
            finally
            {
                Debug.WriteLine($"{DateTime.Now:s}\tWriting settings STOP");
                Settings.SettingsReadWriteLock.ExitWriteLock();
            }
        }

        private static void BackupCfg(Settings settings)
        {
            try
            {
                var ser = new XmlSerializer(typeof(Settings));
                var configPath1Hour = Path.Combine(Folders.Backup, "config.cfg.Backup1");
                if (!File.Exists(configPath1Hour) || (DateTime.UtcNow - File.GetLastWriteTimeUtc(configPath1Hour)).TotalHours > 1)
                {
                    using TextWriter writer = new StreamWriter(configPath1Hour);
                    ser.Serialize(writer, settings);
                    writer.Close();
                }

                var configPath48Hour = Path.Combine(Folders.Backup, "config.cfg.Backup2");
                if (!File.Exists(configPath48Hour) || (DateTime.UtcNow - File.GetLastWriteTimeUtc(configPath48Hour)).TotalHours > 48)
                {
                    using TextWriter writer = new StreamWriter(configPath48Hour);
                    ser.Serialize(writer, settings);
                    writer.Close();
                }

                var currentDateTime = DateTimeOffset.Now.UtcDateTime;
                var cultureInfo = CultureInfo.CurrentCulture;
                var calendar = cultureInfo.Calendar;
                var calendarWeekRule = cultureInfo.DateTimeFormat.CalendarWeekRule;
                var firstDayOfWeek = cultureInfo.DateTimeFormat.FirstDayOfWeek;

                // Get the week number of the year
                var weekOfYear = calendar.GetWeekOfYear(currentDateTime, calendarWeekRule, firstDayOfWeek);

                var configPathNDays = Path.Combine(Folders.Backup, $"config.cfg.week{weekOfYear}.zip");
                if (File.Exists(configPathNDays))
                {
                    return;
                }
                BackupToZipFile(configPathNDays);
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("Saving settings backup", ex);
            }
        }

        //[Obfuscation(Exclude = true)]

        private void SetRunOnStartupSetting()
        {
            var rk = Registry.CurrentUser.OpenSubKey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run", true);

            if (barCheckItemStartOnBoot.Checked)
            {
                rk?.SetValue("uBuyFirst", $"\"{Application.ExecutablePath}\"");
                // If application is being launched don't launch Trial Prompt
                rk?.DeleteValue("uBuyFirstTrialPrompt", false);
            }
            else
            {
                rk?.DeleteValue("uBuyFirst", false);
                // Add Trial Prompt so runs on reboot back in
                rk?.SetValue("uBuyFirstTrialPrompt", $"\"{Application.ExecutablePath}\" formtrialsub");
            }
        }

        private void GetRunOnStartupSetting()
        {
            var rk = Registry.CurrentUser.OpenSubKey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run", true);
            var startKey = rk?.GetValue("uBuyFirst");
            barCheckItemStartOnBoot.Checked = startKey != null;
        }

        // ReSharper restore InconsistentNaming
    }
}
