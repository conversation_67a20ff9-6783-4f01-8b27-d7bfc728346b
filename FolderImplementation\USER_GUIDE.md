# Keyword Folders - User Guide

## 🎯 Overview

The keyword folder system allows you to organize your eBay search keywords into hierarchical folder structures, making it easier to manage large numbers of keywords.

## 🚀 Getting Started

### What You'll See

After the folder implementation, your keyword TreeList now supports:
- **Folders** (displayed in bold blue text with folder icons)
- **Keywords** (displayed normally within folders)
- **Nested folder structures** (unlimited depth)

### Automatic Migration

When you first use the new version:
- All existing keywords are automatically moved to a "Default" folder
- No keywords are lost or modified
- All existing functionality continues to work exactly as before

## 📁 Working with Folders

### Creating Folders

1. **Right-click** in an empty area of the TreeList or on an existing folder
2. Select **"New Folder"** from the context menu
3. A new folder named "New Folder" will be created
4. **Rename it immediately** by clicking on the name and typing a new name
5. Press **Enter** to confirm the name

### Renaming Folders

1. **Right-click** on the folder you want to rename
2. Select **"Rename Folder"** from the context menu
3. The folder name becomes editable
4. Type the new name and press **Enter**

### Deleting Folders

1. **Right-click** on the folder you want to delete
2. Select **"Delete Folder"** from the context menu
3. **Confirm** the deletion in the dialog box
4. ⚠️ **Warning**: This will delete the folder and ALL keywords inside it!

## ✅ Enabling/Disabling Folders

### Folder Checkbox Behavior

When you check or uncheck a folder's checkbox:
- **Checking a folder** enables ALL keywords within that folder and all subfolders
- **Unchecking a folder** disables ALL keywords within that folder and all subfolders
- **Folder state indicators**:
  - ✅ **Checked**: All keywords in folder are enabled
  - ❌ **Unchecked**: All keywords in folder are disabled
  - ◐ **Indeterminate**: Some keywords enabled, some disabled

### How to Enable/Disable Folders

1. **Click the checkbox** next to any folder name
2. The change will **cascade automatically** to all contents
3. **All keywords** in that folder and subfolders will be enabled/disabled
4. **Subfolder states** will update to reflect the change

### Example Scenario

```
📁 Electronics [◐ Indeterminate]
  📁 Mobile Phones [✅ Checked]
    🔍 iPhone 15 [✅ Enabled]
    🔍 Samsung Galaxy [✅ Enabled]
  📁 Laptops [❌ Unchecked]
    🔍 MacBook Pro [❌ Disabled]
    🔍 Dell XPS [❌ Disabled]
```

If you **check** the "Electronics" folder:
- All keywords become enabled
- All subfolders show as checked
- The parent folder shows as checked

## 🔄 Organizing Keywords

### Moving Keywords to Folders

**Method 1: Drag & Drop**
1. **Click and hold** on a keyword
2. **Drag** it over the target folder
3. **Release** to drop the keyword into the folder

**Method 2: Cut and Paste** (if drag & drop isn't working)
1. **Right-click** on the keyword
2. Select **"Cut"** (if available)
3. **Right-click** on the target folder
4. Select **"Paste"** (if available)

### Moving Folders

You can also move entire folders (with all their contents):
1. **Drag** a folder onto another folder to make it a subfolder
2. **Drag** a folder to an empty area to make it a root-level folder

### Creating Nested Structures

Example organization:
```
📁 Electronics
  📁 Mobile Phones
    🔍 iPhone 15
    🔍 Samsung Galaxy
  📁 Laptops
    🔍 MacBook Pro
    🔍 Dell XPS
📁 Clothing
  📁 Men's
    🔍 Nike Shoes
  📁 Women's
    🔍 Designer Handbags
```

## 📊 CSV Export/Import with Folders

### Exporting Keywords

When you export keywords to CSV:
1. A new **"Folder Path"** column is included
2. Folder paths use the format: `Electronics > Mobile Phones`
3. Keywords without folders have an empty folder path

### Importing Keywords

When importing CSV files:
1. Include a **"Folder Path"** column in your CSV
2. Use the format: `Parent Folder > Child Folder > Grandchild Folder`
3. Folders will be **automatically created** if they don't exist
4. Leave the folder path empty to place keywords at the root level

**Example CSV:**
```csv
Id,Folder Path,eBay Search Alias,Keywords,Keyword enabled
1,Electronics > Mobile Phones,iPhone Search,iPhone 15 Pro,true
2,Electronics > Laptops,MacBook Search,MacBook Pro M3,true
3,,Root Keyword,search terms,true
```

## 💡 Tips and Best Practices

### Organization Strategies

1. **By Category**: Electronics, Clothing, Home & Garden
2. **By Brand**: Apple, Samsung, Nike, Adidas
3. **By Price Range**: Under $50, $50-$100, Over $100
4. **By Priority**: High Priority, Medium Priority, Low Priority
5. **By Status**: Active, Testing, Disabled

### Folder Naming

- Use **clear, descriptive names**
- Keep names **short but meaningful**
- Use **consistent naming conventions**
- Consider using **prefixes** for sorting (e.g., "01-Electronics", "02-Clothing")

### Performance Tips

- **Collapse unused folders** to improve visual clarity
- **Group related keywords** together for easier management
- **Use nested folders** sparingly - 2-3 levels is usually sufficient

## 🔧 Troubleshooting

### Common Issues

**Keywords disappeared after update**
- Check the "Default" folder - all existing keywords are moved there automatically

**Can't create folders**
- Make sure you're right-clicking in the TreeList area
- Try right-clicking on an existing folder instead of empty space

**Drag & drop not working**
- Make sure you're dragging onto a folder (bold blue text)
- Try dragging more slowly and waiting for visual feedback

**CSV import not creating folders**
- Check that your "Folder Path" column header is exactly: `Folder Path`
- Use the exact format: `Parent > Child > Grandchild`
- Make sure there are no extra spaces around the `>` separator

### Getting Help

If you encounter issues:
1. Check that all existing functionality still works normally
2. Try restarting the application
3. Check the application logs for error messages
4. Contact support with specific details about what you were trying to do

## 🎉 Enjoy Your Organized Keywords!

The folder system is designed to make keyword management easier and more intuitive. Start by organizing your most frequently used keywords, then gradually build out your folder structure as needed.

Remember: **All existing functionality remains unchanged** - folders are purely an organizational tool that enhances your workflow without disrupting it.
